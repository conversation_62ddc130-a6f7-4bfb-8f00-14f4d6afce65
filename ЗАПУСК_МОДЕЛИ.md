# 🚀 Быстрый запуск генеративной модели на GTX 1080

## ✅ Статус установки

- ✅ **PyTorch 2.5.1+cu121** - Установлен
- ⏳ **vLLM** - Устанавливается (может занять 10-15 минут)
- ✅ **CUDA 12.6** - Готов
- ✅ **GPU: GTX 1080 (8GB VRAM)** - Обнаружен

## 🎯 Рекомендуемые модели для GTX 1080 (8GB VRAM)

### 1. **Phi-3 Mini** (Рекомендуется)
- **Размер**: ~4GB VRAM
- **Скорость**: 35-50 токенов/сек
- **Качество**: Отличное для размера

### 2. **TinyLlama**
- **Размер**: ~1GB VRAM  
- **Скорость**: 80-120 токенов/сек
- **Качество**: Хорошее для быстрых ответов

### 3. **Mistral 7B AWQ** (Квантованная)
- **Размер**: ~4GB VRAM
- **Скорость**: 20-30 токенов/сек
- **Качество**: Высокое

## 🚀 Команды для запуска

### После завершения установки vLLM:

#### 1. Активация окружения
```bash
source vllm-env/bin/activate
```

#### 2. Тест установки
```bash
python test_model.py
```

#### 3. Запуск сервера
```bash
# Phi-3 Mini (рекомендуется)
python start_server.py --model microsoft/Phi-3-mini-4k-instruct

# Или интерактивный выбор
python start_server.py
```

#### 4. Тест клиента
```bash
# В новом терминале
python test_client.py --mode test
```

#### 5. Интерактивный чат
```bash
python test_client.py --mode chat
```

## 📊 Ожидаемая производительность

| Модель | VRAM | Токены/сек | Время ответа |
|--------|------|------------|--------------|
| TinyLlama | 1GB | 80-120 | ~1-2 сек |
| Phi-3 Mini | 4GB | 35-50 | ~2-4 сек |
| Mistral 7B AWQ | 4GB | 20-30 | ~3-6 сек |

## 🔧 Быстрые команды

```bash
# Проверка GPU
nvidia-smi

# Активация окружения
source vllm-env/bin/activate

# Быстрый тест
python -c "import torch; print(f'CUDA: {torch.cuda.is_available()}')"

# Запуск простейшей модели
python start_server.py --model TinyLlama/TinyLlama-1.1B-Chat-v1.0

# Тест API
curl http://localhost:8000/health
```

## 🆘 Если что-то не работает

### 1. Проблемы с памятью
```bash
# Используйте более маленькую модель
python start_server.py --model TinyLlama/TinyLlama-1.1B-Chat-v1.0
```

### 2. Медленная работа
```bash
# Проверьте загрузку GPU
nvidia-smi -l 1
```

### 3. Ошибки установки
```bash
# Переустановка vLLM
source vllm-env/bin/activate
pip uninstall vllm
pip install vllm
```

## 📚 Полная документация

- `README_VLLM.md` - Подробное руководство
- `docs/VLLM_SETUP_GUIDE.md` - Полная инструкция по установке
- `docs/VLLM_PERFORMANCE_GUIDE.md` - Оптимизация производительности
- `docs/VLLM_TROUBLESHOOTING.md` - Решение проблем

## 🎉 Готово!

После завершения установки vLLM вы сможете:

1. **Запустить локальный AI сервер** на порту 8000
2. **Использовать REST API** для генерации текста
3. **Интегрировать с вашими приложениями**
4. **Получить скорость 20-50 токенов/сек** на GTX 1080

---

**💡 Совет**: Начните с TinyLlama для быстрого тестирования, затем переходите к Phi-3 Mini для лучшего качества.

**⚡ Статус**: Установка vLLM в процессе... Ожидайте завершения!
