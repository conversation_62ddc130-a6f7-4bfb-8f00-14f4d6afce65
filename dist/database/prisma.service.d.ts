import { PrismaClient } from '@prisma/client';
export declare class PrismaService {
    private static instance;
    private prisma;
    private constructor();
    static getInstance(): PrismaService;
    getClient(): PrismaClient;
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    healthCheck(): Promise<boolean>;
    transaction<T>(fn: (prisma: any) => Promise<T>): Promise<T>;
    getStats(): Promise<any>;
    cleanup(): Promise<void>;
}
export declare const prismaService: PrismaService;
//# sourceMappingURL=prisma.service.d.ts.map