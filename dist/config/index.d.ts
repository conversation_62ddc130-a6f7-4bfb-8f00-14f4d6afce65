export declare const config: {
    TELEGRAM_BOT_TOKEN: string;
    PROXYAPI_KEY: string;
    PROXYAPI_IMAGE_MODEL: string;
    IMAGE_OUTPUT_WIDTH: number;
    IMAGE_OUTPUT_HEIGHT: number;
    IMAGE_QUALITY: "low" | "medium" | "high";
    OPENROUTER_API_KEY: string;
    OPENROUTER_MODEL_VISION: string;
    OPENROUTER_MODEL_TEXT: string;
    DATABASE_URL: string;
    PORT: number;
    NODE_ENV: "development" | "production" | "test";
    UPLOAD_DIR: string;
    MAX_FILE_SIZE: number;
    LOG_LEVEL: "error" | "warn" | "info" | "debug";
    LOG_FILE: string;
    RATE_LIMIT_WINDOW_MS: number;
    RATE_LIMIT_MAX_REQUESTS: number;
    CACHE_TTL: number;
    REDIS_URL?: string | undefined;
    ABCP_API_KEY?: string | undefined;
    EXIST_API_KEY?: string | undefined;
    AUTODOC_API_KEY?: string | undefined;
};
export declare const proxyAPIConfig: {
    readonly apiKey: string;
    readonly baseURL: "https://api.proxyapi.ru/openai/v1";
    readonly models: {
        readonly image: string;
    };
};
export declare const imageConfig: {
    readonly outputWidth: number;
    readonly outputHeight: number;
    readonly quality: "low" | "medium" | "high";
    readonly aspectRatio: number;
    readonly supportedSizes: readonly ["1920x1024", "1536x1024", "1024x1536", "1024x1024"];
};
export declare const openRouterConfig: {
    readonly apiKey: string;
    readonly baseURL: "https://openrouter.ai/api/v1";
    readonly models: {
        readonly vision: string;
        readonly text: string;
    };
};
export declare const telegramConfig: {
    readonly token: string;
    readonly options: {
        readonly polling: true;
        readonly filepath: false;
    };
};
export declare const databaseConfig: {
    readonly url: string;
};
export declare const serverConfig: {
    readonly port: number;
    readonly env: "development" | "production" | "test";
    readonly uploadDir: string;
    readonly maxFileSize: number;
};
export declare const loggingConfig: {
    readonly level: "error" | "warn" | "info" | "debug";
    readonly file: string;
};
export declare const rateLimitConfig: {
    readonly windowMs: number;
    readonly maxRequests: number;
};
export declare const cacheConfig: {
    readonly redisUrl: string | undefined;
    readonly ttl: number;
};
export declare const externalApiConfig: {
    readonly abcp: {
        readonly apiKey: string | undefined;
        readonly baseURL: "https://api.abcp.ru";
    };
    readonly exist: {
        readonly apiKey: string | undefined;
        readonly baseURL: "https://api.exist.ru";
    };
    readonly autodoc: {
        readonly apiKey: string | undefined;
        readonly baseURL: "https://api.autodoc.com";
    };
};
//# sourceMappingURL=index.d.ts.map