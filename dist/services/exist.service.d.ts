export interface ExistPartInfo {
    id: string;
    brand: string;
    partNumber: string;
    name: string;
    price: number;
    currency: string;
    availability: 'in_stock' | 'order' | 'out_of_stock';
    deliveryDays: number;
    description?: string;
    imageUrl?: string;
    weight?: number;
    oem?: string[];
}
export interface ExistSearchParams {
    query: string;
    brand?: string;
    category?: string;
    minPrice?: number;
    maxPrice?: number;
    limit?: number;
}
export interface ExistSearchResponse {
    parts: ExistPartInfo[];
    total: number;
    categories: string[];
}
export declare class ExistService {
    private client;
    private readonly baseURL;
    constructor();
    searchParts(params: ExistSearchParams): Promise<ExistSearchResponse>;
    getPartDetails(partId: string): Promise<ExistPartInfo | null>;
    searchByOEM(oemNumber: string): Promise<ExistPartInfo[]>;
    getCarCategories(make: string, model: string, year: number): Promise<string[]>;
    checkHealth(): Promise<boolean>;
    private mapAvailability;
}
//# sourceMappingURL=exist.service.d.ts.map