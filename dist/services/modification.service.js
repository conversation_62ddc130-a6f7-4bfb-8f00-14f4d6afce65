"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModificationService = void 0;
const types_1 = require("@/types");
const openai_service_1 = require("./openai.service");
const car_recognition_service_1 = require("./car-recognition.service");
const pricing_service_1 = require("./pricing.service");
const validation_1 = require("@/utils/validation");
const logger_1 = require("@/utils/logger");
class ModificationService {
    constructor() {
        this.openAIService = new openai_service_1.OpenAIService();
        this.carRecognitionService = new car_recognition_service_1.CarRecognitionService();
        this.pricingService = new pricing_service_1.PricingService();
    }
    async recognizeCar(imageUrl) {
        return await this.carRecognitionService.recognizeCar(imageUrl);
    }
    async generateModifications(originalImageUrl, carInfo, userRequest) {
        try {
            logger_1.logger.info(`Generating modifications for ${carInfo.make} ${carInfo.model}`);
            const requestedModifications = this.parseModificationRequest(userRequest);
            const modifiedImage = await this.openAIService.generateModifiedImage(originalImageUrl, carInfo, requestedModifications, userRequest);
            let analysisDescription;
            if (modifiedImage.url === originalImageUrl) {
                analysisDescription = this.generateModificationDescription(carInfo, requestedModifications, userRequest);
            }
            else {
                analysisDescription = await this.openAIService.analyzeModifications(originalImageUrl, modifiedImage.url, carInfo);
            }
            const appliedModifications = await this.generateAppliedModificationsList(carInfo, requestedModifications, analysisDescription);
            const totalCost = this.calculateTotalCost(appliedModifications);
            const installationNotes = this.generateInstallationNotes(appliedModifications);
            const result = {
                id: `mod_${Date.now()}`,
                originalImageUrl,
                modifiedImageUrl: modifiedImage.url,
                appliedModifications,
                totalCost,
                description: analysisDescription,
                installationNotes,
            };
            logger_1.logger.info('Modification generation completed successfully');
            return result;
        }
        catch (error) {
            logger_1.logger.error('Error generating modifications:', error);
            throw new Error('Failed to generate modifications');
        }
    }
    generateModificationDescription(carInfo, modifications, userRequest) {
        const carDescription = `${carInfo.year} ${carInfo.make} ${carInfo.model}`;
        let description = `Proposed modifications for your ${carDescription}:\n\n`;
        if (modifications.length > 0) {
            description += "Recommended modifications:\n";
            modifications.forEach((mod, index) => {
                description += `${index + 1}. ${this.formatModificationName(mod)}\n`;
            });
        }
        if (userRequest) {
            description += `\nBased on your request: "${userRequest}"\n`;
        }
        description += `\nThese modifications would enhance the appearance and performance of your ${carDescription} while maintaining its original character and design language.`;
        return description;
    }
    parseModificationRequest(userRequest) {
        const sanitizedRequest = validation_1.ValidationUtils.sanitizeText(userRequest);
        if (!validation_1.ValidationUtils.validateModificationText(sanitizedRequest)) {
            logger_1.logger.warn('Invalid modification request received:', userRequest);
            return ['sport modification package'];
        }
        const extractedKeywords = validation_1.ValidationUtils.extractModificationKeywords(sanitizedRequest);
        if (extractedKeywords.length > 0) {
            return extractedKeywords;
        }
        const request = sanitizedRequest.toLowerCase();
        const modifications = [];
        if (request.includes('body kit') || request.includes('bodykit')) {
            modifications.push('sport body kit');
        }
        if (request.includes('front splitter') || request.includes('splitter')) {
            modifications.push('front splitter');
        }
        if (request.includes('side skirts') || request.includes('skirts')) {
            modifications.push('side skirts');
        }
        if (request.includes('wheels') || request.includes('rims') || request.includes('alloys')) {
            if (request.includes('black')) {
                modifications.push('black sport wheels');
            }
            else if (request.includes('chrome')) {
                modifications.push('chrome wheels');
            }
            else {
                modifications.push('sport wheels');
            }
        }
        if (request.includes('spoiler') || request.includes('wing')) {
            if (request.includes('rear')) {
                modifications.push('rear spoiler');
            }
            else {
                modifications.push('spoiler');
            }
        }
        if (request.includes('lower') || request.includes('lowered') || request.includes('suspension')) {
            modifications.push('lowered suspension');
        }
        if (request.includes('exhaust') || request.includes('pipes')) {
            modifications.push('sport exhaust system');
        }
        if (request.includes('aggressive') || request.includes('sport')) {
            if (!modifications.some(m => m.includes('body kit'))) {
                modifications.push('aggressive body kit');
            }
        }
        if (modifications.length === 0) {
            modifications.push('sport modification package');
        }
        return modifications;
    }
    async generateAppliedModificationsList(carInfo, requestedModifications, analysisDescription) {
        const appliedModifications = [];
        for (const modification of requestedModifications) {
            const modificationData = await this.createModificationData(carInfo, modification, analysisDescription);
            if (modificationData) {
                appliedModifications.push(modificationData);
            }
        }
        return appliedModifications;
    }
    async createModificationData(carInfo, modification, analysisDescription) {
        try {
            const pricing = await this.pricingService.getModificationPricing(carInfo, modification);
            const modificationData = {
                type: {
                    id: `mod_${modification.replace(/\s+/g, '_')}`,
                    name: this.formatModificationName(modification),
                    category: this.categorizeModification(modification),
                    description: `${this.formatModificationName(modification)} for ${carInfo.make} ${carInfo.model}`,
                    averagePrice: pricing.averagePrice,
                    installationComplexity: this.getInstallationComplexity(modification),
                    compatibleWith: [`${carInfo.make} ${carInfo.model}`],
                },
                specificPart: pricing.specificPart,
                brand: pricing.brand,
                partNumber: pricing.partNumber,
                price: pricing.price,
                installationTime: pricing.installationTime,
                description: `${this.formatModificationName(modification)} - ${pricing.description}`,
            };
            return modificationData;
        }
        catch (error) {
            logger_1.logger.error(`Error creating modification data for ${modification}:`, error);
            return null;
        }
    }
    formatModificationName(modification) {
        return modification
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }
    categorizeModification(modification) {
        const mod = modification.toLowerCase();
        if (mod.includes('body kit') || mod.includes('splitter') || mod.includes('skirts')) {
            return types_1.ModificationCategory.BODY_KIT;
        }
        if (mod.includes('wheels') || mod.includes('rims')) {
            return types_1.ModificationCategory.WHEELS;
        }
        if (mod.includes('spoiler') || mod.includes('wing')) {
            return types_1.ModificationCategory.SPOILER;
        }
        if (mod.includes('suspension') || mod.includes('lower')) {
            return types_1.ModificationCategory.SUSPENSION;
        }
        if (mod.includes('exhaust')) {
            return types_1.ModificationCategory.EXHAUST;
        }
        return types_1.ModificationCategory.PERFORMANCE;
    }
    getInstallationComplexity(modification) {
        const mod = modification.toLowerCase();
        if (mod.includes('wheels') || mod.includes('rims')) {
            return types_1.InstallationComplexity.EASY;
        }
        if (mod.includes('spoiler') || mod.includes('exhaust')) {
            return types_1.InstallationComplexity.MEDIUM;
        }
        if (mod.includes('body kit') || mod.includes('suspension')) {
            return types_1.InstallationComplexity.HARD;
        }
        return types_1.InstallationComplexity.MEDIUM;
    }
    calculateTotalCost(appliedModifications) {
        const exchangeRate = 95;
        const parts = appliedModifications.reduce((sum, mod) => sum + (mod.price * exchangeRate), 0);
        const labor = appliedModifications.reduce((sum, mod) => {
            const laborMultiplier = {
                [types_1.InstallationComplexity.EASY]: 0.2,
                [types_1.InstallationComplexity.MEDIUM]: 0.5,
                [types_1.InstallationComplexity.HARD]: 1.0,
                [types_1.InstallationComplexity.PROFESSIONAL]: 1.5,
            };
            return sum + (mod.price * exchangeRate * laborMultiplier[mod.type.installationComplexity]);
        }, 0);
        const total = parts + labor;
        return {
            parts: Math.round(parts),
            labor: Math.round(labor),
            total: Math.round(total),
            currency: 'RUB',
            priceRange: {
                min: Math.round(total * 0.8),
                max: Math.round(total * 1.3),
            },
        };
    }
    generateInstallationNotes(appliedModifications) {
        const notes = [];
        const complexModifications = appliedModifications.filter(mod => mod.type.installationComplexity === types_1.InstallationComplexity.HARD ||
            mod.type.installationComplexity === types_1.InstallationComplexity.PROFESSIONAL);
        if (complexModifications.length > 0) {
            notes.push('⚠️ Professional installation recommended for complex modifications');
        }
        const bodyKitMods = appliedModifications.filter(mod => mod.type.category === types_1.ModificationCategory.BODY_KIT);
        if (bodyKitMods.length > 0) {
            notes.push('🔧 Body kit installation may require painting to match car color');
        }
        const suspensionMods = appliedModifications.filter(mod => mod.type.category === types_1.ModificationCategory.SUSPENSION);
        if (suspensionMods.length > 0) {
            notes.push('⚖️ Wheel alignment required after suspension modifications');
        }
        if (notes.length === 0) {
            notes.push('✅ Standard installation procedures apply');
        }
        return notes.join('\n');
    }
}
exports.ModificationService = ModificationService;
//# sourceMappingURL=modification.service.js.map