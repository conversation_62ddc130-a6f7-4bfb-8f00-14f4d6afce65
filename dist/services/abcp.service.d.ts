export interface AbcpPartInfo {
    id: string;
    brand: string;
    number: string;
    name: string;
    price: number;
    currency: string;
    availability: number;
    deliveryDays: number;
    supplier: string;
    weight?: number;
    imageUrl?: string;
}
export interface AbcpSearchParams {
    query: string;
    brand?: string;
    limit?: number;
    offset?: number;
}
export interface AbcpSearchResponse {
    parts: AbcpPartInfo[];
    total: number;
    hasMore: boolean;
}
export declare class AbcpService {
    private client;
    private readonly baseURL;
    constructor();
    searchParts(params: AbcpSearchParams): Promise<AbcpSearchResponse>;
    getPartDetails(partId: string): Promise<AbcpPartInfo | null>;
    findAnalogs(brand: string, partNumber: string): Promise<AbcpPartInfo[]>;
    checkHealth(): Promise<boolean>;
}
//# sourceMappingURL=abcp.service.d.ts.map