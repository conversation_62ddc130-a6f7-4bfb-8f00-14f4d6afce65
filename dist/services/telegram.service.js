"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TelegramService = void 0;
const node_telegram_bot_api_1 = __importDefault(require("node-telegram-bot-api"));
const config_1 = require("@/config");
const types_1 = require("@/types");
const logger_1 = require("@/utils/logger");
const session_service_1 = require("./session.service");
const modification_service_1 = require("./modification.service");
const cost_formatter_service_1 = require("./cost-formatter.service");
const axios_1 = __importDefault(require("axios"));
class TelegramService {
    constructor() {
        this.bot = new node_telegram_bot_api_1.default(config_1.telegramConfig.token, config_1.telegramConfig.options);
        this.sessionManager = new session_service_1.SessionManager();
        this.modificationService = new modification_service_1.ModificationService();
        this.costFormatter = new cost_formatter_service_1.CostFormatterService();
        this.setupHandlers();
    }
    setupHandlers() {
        this.bot.onText(/\/start/, (msg) => {
            this.handleStart(msg);
        });
        this.bot.onText(/\/help/, (msg) => {
            this.handleHelp(msg);
        });
        this.bot.on('photo', (msg) => {
            this.handlePhoto(msg);
        });
        this.bot.on('message', (msg) => {
            if (msg.text && !msg.text.startsWith('/') && !msg.photo) {
                this.handleTextMessage(msg);
            }
        });
        this.bot.on('polling_error', (error) => {
            logger_1.logger.error('Telegram polling error:', error);
        });
        logger_1.logger.info('Telegram bot handlers set up successfully');
    }
    async handleStart(msg) {
        const chatId = msg.chat.id;
        const user = {
            id: msg.from?.id || 0,
            username: msg.from?.username,
            firstName: msg.from?.first_name || 'User',
            lastName: msg.from?.last_name,
            languageCode: msg.from?.language_code,
        };
        await this.sessionManager.createSession(user.id, chatId);
        const welcomeMessage = `
🚗 Welcome to Car Modification Generator! 

I can help you visualize modifications for your car using AI. Here's how it works:

1️⃣ Send me a photo of your car
2️⃣ Tell me what modifications you want (body kit, wheels, spoiler, etc.)
3️⃣ Get a realistic visualization with cost estimates

📸 Send me a car photo to get started!

Use /help for more information.
    `;
        await this.sendMessage(chatId, welcomeMessage);
    }
    async handleHelp(msg) {
        const chatId = msg.chat.id;
        const helpMessage = `
🔧 Car Modification Generator Help

📸 **How to use:**
1. Send a clear photo of your car (side view works best)
2. Wait for car recognition
3. Describe the modifications you want
4. Get your visualization with cost breakdown

🎯 **Supported modifications:**
• Body kits and aerodynamic packages
• Wheels and rims
• Spoilers and wings
• Lowering and suspension
• Exhaust systems
• Lighting modifications

💡 **Tips for best results:**
• Use high-quality, well-lit photos
• Side or 3/4 view angles work best
• Avoid heavily modified cars as base
• Be specific about what you want

📋 **Commands:**
/start - Start over
/help - Show this help
    `;
        await this.sendMessage(chatId, helpMessage);
    }
    async handlePhoto(msg) {
        const chatId = msg.chat.id;
        const userId = msg.from?.id || 0;
        try {
            const session = await this.sessionManager.getSession(userId);
            if (!session || session.currentStep !== types_1.ConversationStep.WAITING_FOR_IMAGE) {
                await this.sendMessage(chatId, "Please start with /start command first, then send your car photo.");
                return;
            }
            const photo = msg.photo?.[msg.photo.length - 1];
            if (!photo) {
                await this.sendMessage(chatId, "❌ No photo received. Please try again.");
                return;
            }
            await this.sendMessage(chatId, "📸 Photo received! Analyzing your car...");
            await this.sessionManager.updateSession(userId, {
                currentStep: types_1.ConversationStep.PROCESSING_IMAGE,
                uploadedImage: photo.file_id,
            });
            const fileUrl = await this.bot.getFileLink(photo.file_id);
            const result = await this.modificationService.recognizeCar(fileUrl);
            if (result.carInfo.confidence < 0.6) {
                await this.sendMessage(chatId, `⚠️ I'm not very confident about the car recognition (${Math.round(result.carInfo.confidence * 100)}% confidence). 
          Please try with a clearer photo or a different angle.`);
                await this.sessionManager.updateSession(userId, {
                    currentStep: types_1.ConversationStep.WAITING_FOR_IMAGE,
                });
                return;
            }
            const carInfo = result.carInfo;
            await this.sessionManager.updateSession(userId, {
                currentStep: types_1.ConversationStep.CAR_RECOGNIZED,
                carInfo: carInfo,
            });
            const recognitionMessage = `
✅ **Car Recognized!**

🚗 **Vehicle:** ${carInfo.year} ${carInfo.make} ${carInfo.model}
📊 **Confidence:** ${Math.round(carInfo.confidence * 100)}%
🏗️ **Body Type:** ${carInfo.bodyType}

💡 **Popular modifications for this car:**
${result.suggestions.map(s => `• ${s}`).join('\n')}

Now tell me what modifications you'd like to see! You can say something like:
"Add a sport body kit and black wheels"
"I want a rear spoiler and lowered suspension"
"Make it look more aggressive with a front splitter"
      `;
            await this.sendMessage(chatId, recognitionMessage);
        }
        catch (error) {
            logger_1.logger.error('Error handling photo:', error);
            await this.sendMessage(chatId, "❌ Sorry, I couldn't process your photo. Please try again with a different image.");
            await this.sessionManager.updateSession(userId, {
                currentStep: types_1.ConversationStep.WAITING_FOR_IMAGE,
            });
        }
    }
    async handleTextMessage(msg) {
        const chatId = msg.chat.id;
        const userId = msg.from?.id || 0;
        const text = msg.text || '';
        try {
            const session = await this.sessionManager.getSession(userId);
            if (!session) {
                await this.sendMessage(chatId, "Please start with /start command first.");
                return;
            }
            if (session.currentStep === types_1.ConversationStep.WAITING_FOR_IMAGE) {
                await this.sendMessage(chatId, "📸 Please send a photo of your car first.");
                return;
            }
            if (session.currentStep === types_1.ConversationStep.CAR_RECOGNIZED) {
                await this.handleModificationRequest(chatId, userId, text, session);
            }
            else {
                await this.sendMessage(chatId, "I'm not sure what you want to do. Use /start to begin.");
            }
        }
        catch (error) {
            logger_1.logger.error('Error handling text message:', error);
            await this.sendMessage(chatId, "❌ Something went wrong. Please try again.");
        }
    }
    async handleModificationRequest(chatId, userId, request, session) {
        if (!session.carInfo || !session.uploadedImage) {
            await this.sendMessage(chatId, "❌ Session error. Please start over with /start");
            return;
        }
        await this.sendMessage(chatId, "🎨 Generating your car modifications... This may take a minute!");
        await this.sessionManager.updateSession(userId, {
            currentStep: types_1.ConversationStep.GENERATING_MODIFICATIONS,
        });
        try {
            const originalImageUrl = await this.bot.getFileLink(session.uploadedImage);
            const result = await this.modificationService.generateModifications(originalImageUrl, session.carInfo, request);
            await this.sessionManager.updateSession(userId, {
                currentStep: types_1.ConversationStep.SHOWING_RESULTS,
            });
            await this.sendModificationResults(chatId, result, originalImageUrl);
        }
        catch (error) {
            logger_1.logger.error('Error generating modifications:', error);
            await this.sendMessage(chatId, "❌ Sorry, I couldn't generate the modifications. Please try a different request.");
            await this.sessionManager.updateSession(userId, {
                currentStep: types_1.ConversationStep.CAR_RECOGNIZED,
            });
        }
    }
    async sendModificationResults(chatId, result, originalImageUrl) {
        try {
            if (result.modifiedImageUrl) {
                logger_1.logger.info(`Sending modified image: ${result.modifiedImageUrl}`);
                try {
                    await this.bot.sendPhoto(chatId, result.modifiedImageUrl, {
                        caption: `🎨 **Ваш автомобиль с модификациями**

✨ Модификации применены успешно!

💡 *Так будет выглядеть ваш автомобиль*`
                    });
                }
                catch (urlError) {
                    logger_1.logger.warn('Failed to send image by URL, trying to download and send as file:', urlError);
                    try {
                        const imageBuffer = await this.downloadImageAsBuffer(result.modifiedImageUrl);
                        await this.bot.sendPhoto(chatId, imageBuffer, {
                            caption: `🎨 **Ваш автомобиль с модификациями**

✨ Модификации применены успешно!

💡 *Так будет выглядеть ваш автомобиль*`
                        });
                    }
                    catch (bufferError) {
                        logger_1.logger.error('Failed to send image as buffer:', bufferError);
                        throw bufferError;
                    }
                }
                if (result.appliedModifications && result.appliedModifications.length > 0) {
                    const costBreakdown = this.costFormatter.formatCostBreakdown(result.appliedModifications, result.totalCost);
                    await this.sendMessage(chatId, costBreakdown);
                }
                if (result.description && result.description.length > 50) {
                    const shortDescription = result.description.length > 500
                        ? result.description.substring(0, 500) + '...'
                        : result.description;
                    await this.sendMessage(chatId, `📝 **Детали модификаций:**\n\n${shortDescription}`);
                }
                await this.sendMessage(chatId, `Хотите попробовать другие модификации? Просто опишите, что хотели бы изменить! 🚗✨`);
            }
            else {
                await this.sendMessage(chatId, `⚠️ **Не удалось создать визуализацию модификаций**

📝 **Запрошенные изменения:**
${result.description}

Попробуйте переформулировать запрос или попробовать позже.`);
                if (result.appliedModifications && result.appliedModifications.length > 0) {
                    const costBreakdown = this.costFormatter.formatCostBreakdown(result.appliedModifications, result.totalCost);
                    await this.sendMessage(chatId, costBreakdown);
                }
            }
        }
        catch (imageError) {
            logger_1.logger.error('Failed to send modified image:', imageError);
            await this.sendMessage(chatId, `⚠️ **Ошибка при отправке изображения**

📝 **Запрошенные модификации:**
${result.description}

Попробуйте ещё раз или переформулируйте запрос.`);
            if (result.appliedModifications && result.appliedModifications.length > 0) {
                try {
                    const costBreakdown = this.costFormatter.formatCostBreakdown(result.appliedModifications, result.totalCost);
                    await this.sendMessage(chatId, costBreakdown);
                }
                catch (costError) {
                    logger_1.logger.error('Failed to send cost breakdown:', costError);
                }
            }
        }
    }
    async downloadImageAsBuffer(imageUrl) {
        try {
            logger_1.logger.info(`Downloading image from: ${imageUrl}`);
            const response = await axios_1.default.get(imageUrl, {
                responseType: 'arraybuffer',
                timeout: 15000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            });
            const buffer = Buffer.from(response.data);
            logger_1.logger.info(`Image downloaded successfully, size: ${buffer.length} bytes`);
            return buffer;
        }
        catch (error) {
            logger_1.logger.error('Failed to download image:', error);
            throw new Error(`Failed to download image: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async sendMessage(chatId, text) {
        try {
            await this.bot.sendMessage(chatId, text, { parse_mode: 'Markdown' });
        }
        catch (error) {
            logger_1.logger.error('Error sending message:', error);
            await this.bot.sendMessage(chatId, text);
        }
    }
    start() {
        logger_1.logger.info('Telegram bot started successfully');
    }
    stop() {
        this.bot.stopPolling();
        logger_1.logger.info('Telegram bot stopped');
    }
}
exports.TelegramService = TelegramService;
//# sourceMappingURL=telegram.service.js.map