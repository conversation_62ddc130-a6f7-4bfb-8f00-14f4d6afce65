{"version": 3, "file": "modification.service.js", "sourceRoot": "", "sources": ["../../src/services/modification.service.ts"], "names": [], "mappings": ";;;AAAA,mCAQiB;AACjB,qDAAiD;AACjD,uEAAkE;AAClE,uDAAmD;AACnD,mDAAqD;AACrD,2CAAwC;AAExC,MAAa,mBAAmB;IAK9B;QACE,IAAI,CAAC,aAAa,GAAG,IAAI,8BAAa,EAAE,CAAC;QACzC,IAAI,CAAC,qBAAqB,GAAG,IAAI,+CAAqB,EAAE,CAAC;QACzD,IAAI,CAAC,cAAc,GAAG,IAAI,gCAAc,EAAE,CAAC;IAC7C,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,QAAgB;QACjC,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;IACjE,CAAC;IAKD,KAAK,CAAC,qBAAqB,CACzB,gBAAwB,EACxB,OAAgB,EAChB,WAAmB;QAEnB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,gCAAgC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;YAG7E,MAAM,sBAAsB,GAAG,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;YAG1E,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAClE,gBAAgB,EAChB,OAAO,EACP,sBAAsB,EACtB,WAAW,CACZ,CAAC;YAGF,IAAI,mBAA2B,CAAC;YAEhC,IAAI,aAAa,CAAC,GAAG,KAAK,gBAAgB,EAAE,CAAC;gBAE3C,mBAAmB,GAAG,IAAI,CAAC,+BAA+B,CAAC,OAAO,EAAE,sBAAsB,EAAE,WAAW,CAAC,CAAC;YAC3G,CAAC;iBAAM,CAAC;gBAEN,mBAAmB,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,oBAAoB,CACjE,gBAAgB,EAChB,aAAa,CAAC,GAAG,EACjB,OAAO,CACR,CAAC;YACJ,CAAC;YAGD,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,gCAAgC,CACtE,OAAO,EACP,sBAAsB,EACtB,mBAAmB,CACpB,CAAC;YAGF,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,CAAC;YAGhE,MAAM,iBAAiB,GAAG,IAAI,CAAC,yBAAyB,CAAC,oBAAoB,CAAC,CAAC;YAE/E,MAAM,MAAM,GAAuB;gBACjC,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE;gBACvB,gBAAgB;gBAChB,gBAAgB,EAAE,aAAa,CAAC,GAAG;gBACnC,oBAAoB;gBACpB,SAAS;gBACT,WAAW,EAAE,mBAAmB;gBAChC,iBAAiB;aAClB,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YAC9D,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAKO,+BAA+B,CACrC,OAAgB,EAChB,aAAuB,EACvB,WAAmB;QAEnB,MAAM,cAAc,GAAG,GAAG,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;QAE1E,IAAI,WAAW,GAAG,mCAAmC,cAAc,OAAO,CAAC;QAE3E,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,WAAW,IAAI,8BAA8B,CAAC;YAC9C,aAAa,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;gBACnC,WAAW,IAAI,GAAG,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC;YACvE,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YAChB,WAAW,IAAI,6BAA6B,WAAW,KAAK,CAAC;QAC/D,CAAC;QAED,WAAW,IAAI,8EAA8E,cAAc,gEAAgE,CAAC;QAE5K,OAAO,WAAW,CAAC;IACrB,CAAC;IAKO,wBAAwB,CAAC,WAAmB;QAElD,MAAM,gBAAgB,GAAG,4BAAe,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAEnE,IAAI,CAAC,4BAAe,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAChE,eAAM,CAAC,IAAI,CAAC,wCAAwC,EAAE,WAAW,CAAC,CAAC;YACnE,OAAO,CAAC,4BAA4B,CAAC,CAAC;QACxC,CAAC;QAGD,MAAM,iBAAiB,GAAG,4BAAe,CAAC,2BAA2B,CAAC,gBAAgB,CAAC,CAAC;QAExF,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,OAAO,iBAAiB,CAAC;QAC3B,CAAC;QAGD,MAAM,OAAO,GAAG,gBAAgB,CAAC,WAAW,EAAE,CAAC;QAC/C,MAAM,aAAa,GAAa,EAAE,CAAC;QAGnC,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAChE,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACvC,CAAC;QACD,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACvE,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACvC,CAAC;QACD,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClE,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACpC,CAAC;QAGD,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzF,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC9B,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAC3C,CAAC;iBAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACtC,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACN,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QAGD,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5D,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC7B,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACrC,CAAC;iBAAM,CAAC;gBACN,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;QAGD,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAC/F,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC3C,CAAC;QAGD,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC7D,aAAa,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAC7C,CAAC;QAGD,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAChE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC;gBACrD,aAAa,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC;QAGD,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,aAAa,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAKO,KAAK,CAAC,gCAAgC,CAC5C,OAAgB,EAChB,sBAAgC,EAChC,mBAA2B;QAE3B,MAAM,oBAAoB,GAA0B,EAAE,CAAC;QAEvD,KAAK,MAAM,YAAY,IAAI,sBAAsB,EAAE,CAAC;YAClD,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CACxD,OAAO,EACP,YAAY,EACZ,mBAAmB,CACpB,CAAC;YAEF,IAAI,gBAAgB,EAAE,CAAC;gBACrB,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QAED,OAAO,oBAAoB,CAAC;IAC9B,CAAC;IAKO,KAAK,CAAC,sBAAsB,CAClC,OAAgB,EAChB,YAAoB,EACpB,mBAA2B;QAE3B,IAAI,CAAC;YAEH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAC9D,OAAO,EACP,YAAY,CACb,CAAC;YAEF,MAAM,gBAAgB,GAAwB;gBAC5C,IAAI,EAAE;oBACJ,EAAE,EAAE,OAAO,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;oBAC9C,IAAI,EAAE,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC;oBAC/C,QAAQ,EAAE,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC;oBACnD,WAAW,EAAE,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,QAAQ,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE;oBAChG,YAAY,EAAE,OAAO,CAAC,YAAY;oBAClC,sBAAsB,EAAE,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC;oBACpE,cAAc,EAAE,CAAC,GAAG,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;iBACrD;gBACD,YAAY,EAAE,OAAO,CAAC,YAAY;gBAClC,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;gBAC1C,WAAW,EAAE,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,MAAM,OAAO,CAAC,WAAW,EAAE;aACrF,CAAC;YAEF,OAAO,gBAAgB,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;YAC7E,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAKO,sBAAsB,CAAC,YAAoB;QACjD,OAAO,YAAY;aAChB,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACzD,IAAI,CAAC,GAAG,CAAC,CAAC;IACf,CAAC;IAKO,sBAAsB,CAAC,YAAoB;QACjD,MAAM,GAAG,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;QAEvC,IAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnF,OAAO,4BAAoB,CAAC,QAAQ,CAAC;QACvC,CAAC;QACD,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACnD,OAAO,4BAAoB,CAAC,MAAM,CAAC;QACrC,CAAC;QACD,IAAI,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACpD,OAAO,4BAAoB,CAAC,OAAO,CAAC;QACtC,CAAC;QACD,IAAI,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACxD,OAAO,4BAAoB,CAAC,UAAU,CAAC;QACzC,CAAC;QACD,IAAI,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5B,OAAO,4BAAoB,CAAC,OAAO,CAAC;QACtC,CAAC;QAED,OAAO,4BAAoB,CAAC,WAAW,CAAC;IAC1C,CAAC;IAKO,yBAAyB,CAAC,YAAoB;QACpD,MAAM,GAAG,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;QAEvC,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACnD,OAAO,8BAAsB,CAAC,IAAI,CAAC;QACrC,CAAC;QACD,IAAI,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACvD,OAAO,8BAAsB,CAAC,MAAM,CAAC;QACvC,CAAC;QACD,IAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAC3D,OAAO,8BAAsB,CAAC,IAAI,CAAC;QACrC,CAAC;QAED,OAAO,8BAAsB,CAAC,MAAM,CAAC;IACvC,CAAC;IAKO,kBAAkB,CAAC,oBAA2C;QAEpE,MAAM,YAAY,GAAG,EAAE,CAAC;QAExB,MAAM,KAAK,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,GAAG,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7F,MAAM,KAAK,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAErD,MAAM,eAAe,GAAG;gBACtB,CAAC,8BAAsB,CAAC,IAAI,CAAC,EAAE,GAAG;gBAClC,CAAC,8BAAsB,CAAC,MAAM,CAAC,EAAE,GAAG;gBACpC,CAAC,8BAAsB,CAAC,IAAI,CAAC,EAAE,GAAG;gBAClC,CAAC,8BAAsB,CAAC,YAAY,CAAC,EAAE,GAAG;aAC3C,CAAC;YAEF,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,GAAG,YAAY,GAAG,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC;QAC7F,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,MAAM,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;QAE5B,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;YACxB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;YACxB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;YACxB,QAAQ,EAAE,KAAK;YACf,UAAU,EAAE;gBACV,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC;gBAC5B,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC;aAC7B;SACF,CAAC;IACJ,CAAC;IAKO,yBAAyB,CAAC,oBAA2C;QAC3E,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,MAAM,oBAAoB,GAAG,oBAAoB,CAAC,MAAM,CACtD,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,KAAK,8BAAsB,CAAC,IAAI;YAC/D,GAAG,CAAC,IAAI,CAAC,sBAAsB,KAAK,8BAAsB,CAAC,YAAY,CAC/E,CAAC;QAEF,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpC,KAAK,CAAC,IAAI,CAAC,oEAAoE,CAAC,CAAC;QACnF,CAAC;QAED,MAAM,WAAW,GAAG,oBAAoB,CAAC,MAAM,CAC7C,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,KAAK,4BAAoB,CAAC,QAAQ,CAC3D,CAAC;QAEF,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,KAAK,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;QACjF,CAAC;QAED,MAAM,cAAc,GAAG,oBAAoB,CAAC,MAAM,CAChD,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,KAAK,4BAAoB,CAAC,UAAU,CAC7D,CAAC;QAEF,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,KAAK,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;CACF;AAhYD,kDAgYC"}