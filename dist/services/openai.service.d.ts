import { CarInfo, CarRecognitionResponse, OpenAIImageResponse } from '../types/index';
export declare class OpenAIService {
    private proxyAPIService;
    private openRouterService;
    constructor();
    recognizeCar(imageUrl: string): Promise<CarRecognitionResponse>;
    private generateModificationPrompt;
    generateModifiedImage(originalImageUrl: string, carInfo: CarInfo, modifications: string[], userRequest: string): Promise<OpenAIImageResponse>;
    analyzeModifications(originalImageUrl: string, modifiedImageUrl: string, carInfo: CarInfo): Promise<string>;
}
//# sourceMappingURL=openai.service.d.ts.map