{"version": 3, "file": "openrouter.service.js", "sourceRoot": "", "sources": ["../../src/services/openrouter.service.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA6C;AAC7C,2CAAmD;AAEnD,4CAAyC;AAKzC,MAAa,iBAAiB;IAG5B;QACE,IAAI,CAAC,MAAM,GAAG,eAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,wBAAgB,CAAC,OAAO;YACjC,OAAO,EAAE;gBACP,eAAe,EAAE,UAAU,wBAAgB,CAAC,MAAM,EAAE;gBACpD,cAAc,EAAE,kBAAkB;gBAClC,cAAc,EAAE,uBAAuB;gBACvC,SAAS,EAAE,cAAc;aAC1B;YACD,OAAO,EAAE,KAAK;SACf,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,QAAgB;QACjC,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YAExD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC3D,KAAK,EAAE,wBAAgB,CAAC,MAAM,CAAC,MAAM;gBACrC,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE;4BACP;gCACE,IAAI,EAAE,MAAM;gCACZ,IAAI,EAAE;;;;;;;;;;;;2GAYqF;6BAC5F;4BACD;gCACE,IAAI,EAAE,WAAW;gCACjB,SAAS,EAAE;oCACT,GAAG,EAAE,QAAQ;oCACb,MAAM,EAAE,MAAM;iCACf;6BACF;yBACF;qBACF;iBACF;gBACD,UAAU,EAAE,IAAI;gBAChB,WAAW,EAAE,GAAG;aACjB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC;YAC3D,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;YACjD,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE,OAAO,CAAC,CAAC;YAGrD,IAAI,cAAc,CAAC;YACnB,IAAI,CAAC;gBAEH,IAAI,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;gBAGpE,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;gBACpD,IAAI,SAAS,EAAE,CAAC;oBACd,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5C,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;gBAC/C,CAAC;YACH,CAAC;YAAC,OAAO,SAAS,EAAE,CAAC;gBACnB,eAAM,CAAC,IAAI,CAAC,+CAA+C,EAAE,SAAS,CAAC,CAAC;gBAExE,cAAc,GAAG;oBACf,IAAI,EAAE,SAAS;oBACf,KAAK,EAAE,SAAS;oBAChB,IAAI,EAAE,IAAI;oBACV,UAAU,EAAE,SAAS;oBACrB,QAAQ,EAAE,OAAO;oBACjB,UAAU,EAAE,GAAG;oBACf,gBAAgB,EAAE,EAAE;oBACpB,WAAW,EAAE,EAAE;iBAChB,CAAC;YACJ,CAAC;YAED,MAAM,OAAO,GAAY;gBACvB,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE;gBACvB,IAAI,EAAE,cAAc,CAAC,IAAI;gBACzB,KAAK,EAAE,cAAc,CAAC,KAAK;gBAC3B,IAAI,EAAE,cAAc,CAAC,IAAI;gBACzB,UAAU,EAAE,cAAc,CAAC,UAAU;gBACrC,QAAQ,EAAE,cAAc,CAAC,QAAQ;gBACjC,UAAU,EAAE,cAAc,CAAC,UAAU;gBACrC,gBAAgB,EAAE,cAAc,CAAC,gBAAgB,IAAI,EAAE;aACxD,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,mCAAmC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;YAEhG,OAAO;gBACL,OAAO;gBACP,WAAW,EAAE,cAAc,CAAC,WAAW,IAAI,EAAE;gBAC7C,UAAU,EAAE,cAAc,CAAC,UAAU;aACtC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE;gBACrD,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;gBACjE,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;aACxD,CAAC,CAAC;YACH,IAAI,eAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9B,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;oBACxC,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM;oBAC9B,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,UAAU;oBACtC,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI;iBAC3B,CAAC,CAAC;YACL,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,oBAAoB,CACxB,gBAAwB,EACxB,gBAAwB,EACxB,OAAgB;QAEhB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;YAE/D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC3D,KAAK,EAAE,wBAAgB,CAAC,MAAM,CAAC,MAAM;gBACrC,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE;4BACP;gCACE,IAAI,EAAE,MAAM;gCACZ,IAAI,EAAE,wCAAwC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,KAAK;;;;;;;;;sGASL;6BACvF;4BACD;gCACE,IAAI,EAAE,WAAW;gCACjB,SAAS,EAAE,EAAE,GAAG,EAAE,gBAAgB,EAAE,MAAM,EAAE,MAAM,EAAE;6BACrD;4BACD;gCACE,IAAI,EAAE,WAAW;gCACjB,SAAS,EAAE,EAAE,GAAG,EAAE,gBAAgB,EAAE,MAAM,EAAE,MAAM,EAAE;6BACrD;yBACF;qBACF;iBACF;gBACD,UAAU,EAAE,GAAG;gBACf,WAAW,EAAE,GAAG;aACjB,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC;YAC5D,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAC3C,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;YAC/D,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;YACtE,IAAI,eAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9B,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;oBACxC,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM;oBAC9B,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI;iBAC3B,CAAC,CAAC;YACL,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC3D,KAAK,EAAE,wBAAgB,CAAC,MAAM,CAAC,IAAI;gBACnC,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,0DAA0D;qBACpE;iBACF;gBACD,UAAU,EAAE,EAAE;aACf,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC;QAClF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AAtND,8CAsNC"}