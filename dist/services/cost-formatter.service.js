"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CostFormatterService = void 0;
const logger_1 = require("@/utils/logger");
class CostFormatterService {
    formatCostBreakdown(appliedModifications, totalCost) {
        try {
            const formattedParts = appliedModifications.map((mod, index) => this.formatSingleModification(mod, index + 1)).join('\n\n');
            const summary = this.formatCostSummary(totalCost);
            const notes = this.formatNotes();
            return `💰 Стоимость модификаций:

${formattedParts}

${summary}

${notes}`;
        }
        catch (error) {
            logger_1.logger.error('Error formatting cost breakdown:', error);
            return this.getErrorMessage();
        }
    }
    formatSingleModification(modification, index) {
        const partInfo = this.convertToDetailedPartInfo(modification);
        return `${index}. ${partInfo.name}
   💵 Цена: ${this.formatPrice(partInfo.price)} ₽
   🏭 Бренд: ${partInfo.brand}
   ⏱️ Установка: ${partInfo.installationTime}
   📦 Наличие: ${partInfo.availability} ${this.getAvailabilityIcon(partInfo.availability)}
   🚚 Доставка: ${partInfo.deliveryDays} дн.
   🛡️ Гарантия: ${partInfo.warranty}`;
    }
    formatCostSummary(totalCost) {
        return `📊 Итого:
💰 Запчасти: ${this.formatPrice(totalCost.parts)} ₽
🔧 Работы: ${this.formatPrice(totalCost.labor)} ₽
💎 Общая стоимость: ${this.formatPrice(totalCost.total)} ₽`;
    }
    formatNotes() {
        return `📝 Примечания:
• Цены указаны в российских рублях
• Стоимость работ может варьироваться в зависимости от региона
• Доставка рассчитывается отдельно`;
    }
    convertToDetailedPartInfo(modification) {
        return {
            name: modification.specificPart,
            brand: modification.brand,
            price: this.convertToRubles(modification.price),
            installationTime: modification.installationTime,
            availability: this.getRandomAvailability(),
            deliveryDays: this.getRandomDeliveryDays(),
            warranty: this.getWarrantyPeriod(modification.type.category),
            partNumber: modification.partNumber,
            description: modification.description
        };
    }
    convertToRubles(usdPrice) {
        const exchangeRate = 95;
        return Math.round(usdPrice * exchangeRate);
    }
    formatPrice(price) {
        return price.toLocaleString('ru-RU');
    }
    getAvailabilityIcon(availability) {
        switch (availability) {
            case 'В наличии':
                return '✅';
            case 'Под заказ':
                return '🔄';
            case 'Нет в наличии':
                return '❌';
            default:
                return '❓';
        }
    }
    getRandomAvailability() {
        const statuses = [
            'В наличии', 'В наличии', 'В наличии',
            'Под заказ', 'Под заказ',
            'Нет в наличии'
        ];
        return statuses[Math.floor(Math.random() * statuses.length)];
    }
    getRandomDeliveryDays() {
        return Math.floor(Math.random() * 7) + 1;
    }
    getWarrantyPeriod(category) {
        const warranties = {
            'body_kit': '12 месяцев',
            'wheels': '24 месяца',
            'spoiler': '6 месяцев',
            'exhaust': '12 месяцев',
            'suspension': '24 месяца',
            'interior': '6 месяцев',
            'lighting': '12 месяцев',
            'performance': '6 месяцев'
        };
        return warranties[category] || '6 месяцев';
    }
    getErrorMessage() {
        return `💰 Стоимость модификаций:

❌ Ошибка при расчете стоимости

📝 Примечания:
• Попробуйте повторить запрос
• Обратитесь к администратору если проблема повторяется`;
    }
    formatSimpleCostSummary(totalCost) {
        return `💰 Общая стоимость: ${this.formatPrice(totalCost.total)} ₽
📊 Запчасти: ${this.formatPrice(totalCost.parts)} ₽ | Работы: ${this.formatPrice(totalCost.labor)} ₽`;
    }
}
exports.CostFormatterService = CostFormatterService;
//# sourceMappingURL=cost-formatter.service.js.map