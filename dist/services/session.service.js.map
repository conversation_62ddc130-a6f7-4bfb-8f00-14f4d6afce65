{"version": 3, "file": "session.service.js", "sourceRoot": "", "sources": ["../../src/services/session.service.ts"], "names": [], "mappings": ";;;AAAA,mCAAiE;AACjE,2CAAwC;AAMxC,MAAa,cAAc;IAIzB;QAHQ,aAAQ,GAA6B,IAAI,GAAG,EAAE,CAAC;QACtC,oBAAe,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAIhD,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAChC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACpB,CAAC;IAKD,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,MAAc;QAChD,MAAM,OAAO,GAAgB;YAC3B,MAAM;YACN,MAAM;YACN,WAAW,EAAE,wBAAgB,CAAC,iBAAiB;YAC/C,YAAY,EAAE,IAAI,IAAI,EAAE;SACzB,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACnC,eAAM,CAAC,IAAI,CAAC,4BAA4B,MAAM,EAAE,CAAC,CAAC;QAElD,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,KAAK,CAAC,UAAU,CAAC,MAAc;QAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE1C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QAEhE,IAAI,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YACpC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC7B,eAAM,CAAC,IAAI,CAAC,4BAA4B,MAAM,EAAE,CAAC,CAAC;YAClD,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;QAC3B,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,KAAK,CAAC,aAAa,CACjB,MAAc,EACd,OAAwD;QAExD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAE9C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,eAAM,CAAC,IAAI,CAAC,qDAAqD,MAAM,EAAE,CAAC,CAAC;YAC3E,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE,YAAY,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;QAE9D,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACnC,eAAM,CAAC,KAAK,CAAC,4BAA4B,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;QAE5D,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,KAAK,CAAC,aAAa,CAAC,MAAc;QAChC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAE7C,IAAI,OAAO,EAAE,CAAC;YACZ,eAAM,CAAC,IAAI,CAAC,4BAA4B,MAAM,EAAE,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,KAAK,CAAC,iBAAiB;QACrB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;IAC5C,CAAC;IAKO,sBAAsB;QAC5B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;YACxD,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAEhE,IAAI,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;gBACpC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBAC7B,YAAY,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAED,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACrB,eAAM,CAAC,IAAI,CAAC,cAAc,YAAY,mBAAmB,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe;QAInB,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QACpD,MAAM,MAAM,GAAqC;YAC/C,CAAC,wBAAgB,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACvC,CAAC,wBAAgB,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACtC,CAAC,wBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC;YACpC,CAAC,wBAAgB,CAAC,yBAAyB,CAAC,EAAE,CAAC;YAC/C,CAAC,wBAAgB,CAAC,wBAAwB,CAAC,EAAE,CAAC;YAC9C,CAAC,wBAAgB,CAAC,eAAe,CAAC,EAAE,CAAC;SACtC,CAAC;QAEF,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,KAAK,EAAE,QAAQ,CAAC,MAAM;YACtB,MAAM;SACP,CAAC;IACJ,CAAC;CACF;AA/ID,wCA+IC"}