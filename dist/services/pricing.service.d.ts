import { CarInfo } from '@/types';
export interface ModificationPricing {
    specificPart: string;
    brand: string;
    partNumber?: string;
    price: number;
    averagePrice: number;
    installationTime: string;
    description: string;
    availability?: string;
    deliveryDays?: number;
    warranty?: string;
    material?: string;
    origin?: string;
}
export declare class PricingService {
    private priceDatabase;
    private abcpService;
    private existService;
    private priceCache;
    private readonly CACHE_TTL;
    private prisma;
    constructor();
    getModificationPricing(carInfo: CarInfo, modification: string): Promise<ModificationPricing>;
    private generateModificationKey;
    private getPricingFromDatabase;
    private selectBestPartMatch;
    private getLegacyPricingFromDatabase;
    private getDefaultPricing;
    private formatModificationName;
    private randomPrice;
    private getExternalModificationPricing;
    private searchInABCP;
    private searchInExist;
    private buildSearchQuery;
    private selectBestPart;
    private selectBestExistPart;
    private convertAbcpPartToPricing;
    private convertExistPartToPricing;
    private estimateInstallationTime;
    private getCachedPrice;
    private setCachedPrice;
    private initializePriceDatabase;
    getExternalPricing(carInfo: CarInfo, partNumber: string): Promise<ModificationPricing | null>;
    private getAutodocPricing;
    private getExistPricing;
    getPriceComparison(carInfo: CarInfo, modification: string): Promise<ModificationPricing[]>;
}
//# sourceMappingURL=pricing.service.d.ts.map