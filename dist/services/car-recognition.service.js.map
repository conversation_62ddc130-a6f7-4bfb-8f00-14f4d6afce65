{"version": 3, "file": "car-recognition.service.js", "sourceRoot": "", "sources": ["../../src/services/car-recognition.service.ts"], "names": [], "mappings": ";;;AACA,qDAAiD;AACjD,mDAAqD;AACrD,2CAAwC;AAKxC,MAAa,qBAAqB;IAIhC;QAFQ,gBAAW,GAAqB,IAAI,GAAG,EAAE,CAAC;QAGhD,IAAI,CAAC,aAAa,GAAG,IAAI,8BAAa,EAAE,CAAC;QACzC,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,QAAgB;QACjC,IAAI,CAAC;YAEH,IAAI,CAAC,4BAAe,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC/C,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAChD,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YAGjD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAGpE,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAGvE,MAAM,mBAAmB,GAAG,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,CAAC;YAG9E,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CACnD,WAAW,CAAC,UAAU,EACtB,eAAe,CAChB,CAAC;YAEF,MAAM,MAAM,GAA2B;gBACrC,OAAO,EAAE;oBACP,GAAG,eAAe;oBAClB,UAAU,EAAE,eAAe;iBAC5B;gBACD,WAAW,EAAE,mBAAmB;gBAChC,UAAU,EAAE,eAAe;aAC5B,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,uCAAuC,MAAM,CAAC,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;YAClG,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,cAAc,CAAC,OAAgB;QAC3C,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,4BAAe,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAG/D,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAClE,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAGrE,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,cAAc,EAAE,eAAe,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;YAG7F,MAAM,QAAQ,GAAY;gBACxB,GAAG,aAAa;gBAChB,IAAI,EAAE,cAAc;gBACpB,KAAK,EAAE,eAAe;gBACtB,UAAU,EAAE,MAAM,EAAE,UAAU,IAAI,aAAa,CAAC,UAAU;gBAC1D,QAAQ,EAAE,MAAM,EAAE,QAAQ,IAAI,aAAa,CAAC,QAAQ;gBACpD,gBAAgB,EAAE;oBAChB,GAAG,aAAa,CAAC,gBAAgB;oBACjC,GAAG,CAAC,MAAM,EAAE,cAAc,IAAI,EAAE,CAAC;iBAClC,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,KAAK,CAAC;aACtE,CAAC;YAEF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACjD,OAAO,OAAO,CAAC;QACjB,CAAC;IACH,CAAC;IAKO,2BAA2B,CAAC,OAAgB;QAClD,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;QACxC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QAChD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAG1B,WAAW,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC3C,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAGnC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC/B,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACjC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACvC,CAAC;aAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAC1C,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACjC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACnC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACpC,CAAC;aAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACtE,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACnC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC/B,WAAW,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACxC,CAAC;aAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACtC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACrC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACxC,WAAW,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAC3C,CAAC;QAGD,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACrC,WAAW,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAC5C,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACpC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACtC,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACrC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACjC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjC,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9D,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACvC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACrC,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAClC,WAAW,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAClD,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACrC,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACjC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACpC,CAAC;QAGD,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACvC,WAAW,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QACpD,CAAC;aAAM,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACxB,WAAW,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC/C,WAAW,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAC3C,CAAC;QAGD,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/C,CAAC;IAKO,wBAAwB,CAC9B,cAAsB,EACtB,OAAgB;QAEhB,IAAI,UAAU,GAAG,cAAc,CAAC;QAGhC,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QACpF,IAAI,OAAO,EAAE,CAAC;YACZ,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;QAC/C,CAAC;QAGD,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC7C,IAAI,OAAO,CAAC,IAAI,GAAG,IAAI,IAAI,OAAO,CAAC,IAAI,GAAG,WAAW,EAAE,CAAC;YACtD,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;QAC/C,CAAC;QAGD,IAAI,OAAO,CAAC,gBAAgB,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACzC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC;QAChD,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAC5C,CAAC;IAKO,iBAAiB,CAAC,IAAY;QACpC,MAAM,OAAO,GAA2B;YACtC,IAAI,EAAE,YAAY;YAClB,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,eAAe;YACrB,UAAU,EAAE,eAAe;YAC3B,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,MAAM;YACd,QAAQ,EAAE,QAAQ;YAClB,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE,QAAQ;YAClB,MAAM,EAAE,MAAM;YACd,WAAW,EAAE,WAAW;YACxB,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE,SAAS;YACpB,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE,QAAQ;YAClB,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE,UAAU;YACtB,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE,QAAQ;YAClB,YAAY,EAAE,YAAY;YAC1B,SAAS,EAAE,SAAS;YACpB,SAAS,EAAE,SAAS;YACpB,aAAa,EAAE,aAAa;YAC5B,UAAU,EAAE,UAAU;YACtB,SAAS,EAAE,SAAS;YACpB,aAAa,EAAE,aAAa;SAC7B,CAAC;QAEF,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,IAAI,CAAC;QACvD,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC;IAKO,kBAAkB,CAAC,KAAa;QAEtC,OAAO,KAAK;aACT,OAAO,CAAC,wCAAwC,EAAE,EAAE,CAAC;aACrD,IAAI,EAAE;aACN,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;aACvE,IAAI,CAAC,GAAG,CAAC,CAAC;IACf,CAAC;IAKO,mBAAmB,CAAC,IAAY,EAAE,KAAa,EAAE,IAAY;QACnE,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,IAAI,KAAK,CAAC,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC;QACnE,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACnC,CAAC;IAKO,qBAAqB;QAE3B,MAAM,UAAU,GAAG;YACjB;gBACE,IAAI,EAAE,KAAK;gBACX,KAAK,EAAE,UAAU;gBACjB,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;gBACrC,QAAQ,EAAE,OAAO;gBACjB,UAAU,EAAE,KAAK;gBACjB,cAAc,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,iBAAiB,CAAC;aACnE;YACD;gBACE,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,IAAI;gBACX,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;gBACjD,QAAQ,EAAE,OAAO;gBACjB,UAAU,EAAE,IAAI;gBAChB,cAAc,EAAE,CAAC,eAAe,EAAE,oBAAoB,EAAE,SAAS,CAAC;aACnE;YACD;gBACE,IAAI,EAAE,eAAe;gBACrB,KAAK,EAAE,SAAS;gBAChB,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;gBACrC,QAAQ,EAAE,OAAO;gBACjB,UAAU,EAAE,MAAM;gBAClB,cAAc,EAAE,CAAC,oBAAoB,EAAE,gBAAgB,EAAE,gBAAgB,CAAC;aAC3E;YACD;gBACE,IAAI,EAAE,YAAY;gBAClB,KAAK,EAAE,MAAM;gBACb,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;gBAC/B,QAAQ,EAAE,WAAW;gBACrB,UAAU,EAAE,KAAK;gBACjB,cAAc,EAAE,CAAC,UAAU,EAAE,iBAAiB,EAAE,qBAAqB,CAAC;aACvE;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE,OAAO;gBACd,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;gBAC3C,QAAQ,EAAE,OAAO;gBACjB,UAAU,EAAE,MAAM;gBAClB,cAAc,EAAE,CAAC,sBAAsB,EAAE,cAAc,EAAE,gBAAgB,CAAC;aAC3E;SACF,CAAC;QAGF,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACvB,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACvB,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC;gBAC3E,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE;oBACxB,UAAU,EAAE,GAAG,CAAC,UAAU;oBAC1B,QAAQ,EAAE,GAAG,CAAC,QAAQ;oBACtB,cAAc,EAAE,GAAG,CAAC,cAAc;iBACnC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,eAAM,CAAC,IAAI,CAAC,iCAAiC,IAAI,CAAC,WAAW,CAAC,IAAI,UAAU,CAAC,CAAC;IAChF,CAAC;IAKD,KAAK,CAAC,oBAAoB,CAAC,OAAgB;QACzC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;YAElF,IAAI,CAAC,KAAK,EAAE,CAAC;gBAEX,OAAO;oBACL,gBAAgB,EAAE,SAAS;oBAC3B,cAAc,EAAE,SAAS;oBACzB,UAAU,EAAE,SAAS;oBACrB,gBAAgB,EAAE,SAAS;oBAC3B,SAAS,EAAE,SAAS;iBACrB,CAAC;YACJ,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;CACF;AA3UD,sDA2UC"}