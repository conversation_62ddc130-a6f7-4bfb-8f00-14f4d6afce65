import { UserSession, ConversationStep } from '@/types';
export declare class SessionManager {
    private sessions;
    private readonly SESSION_TIMEOUT;
    constructor();
    createSession(userId: number, chatId: number): Promise<UserSession>;
    getSession(userId: number): Promise<UserSession | null>;
    updateSession(userId: number, updates: Partial<Omit<UserSession, 'userId' | 'chatId'>>): Promise<UserSession | null>;
    deleteSession(userId: number): Promise<boolean>;
    getActiveSessions(): Promise<UserSession[]>;
    private cleanupExpiredSessions;
    getSessionStats(): Promise<{
        total: number;
        byStep: Record<ConversationStep, number>;
    }>;
}
//# sourceMappingURL=session.service.d.ts.map