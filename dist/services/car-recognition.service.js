"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CarRecognitionService = void 0;
const openai_service_1 = require("./openai.service");
const validation_1 = require("@/utils/validation");
const logger_1 = require("@/utils/logger");
class CarRecognitionService {
    constructor() {
        this.carDatabase = new Map();
        this.openAIService = new openai_service_1.OpenAIService();
        this.initializeCarDatabase();
    }
    async recognizeCar(imageUrl) {
        try {
            if (!validation_1.ValidationUtils.isValidImageUrl(imageUrl)) {
                throw new Error('Invalid image URL provided');
            }
            logger_1.logger.info('Starting enhanced car recognition');
            const recognition = await this.openAIService.recognizeCar(imageUrl);
            const enhancedCarInfo = await this.enhanceCarInfo(recognition.carInfo);
            const enhancedSuggestions = this.generateEnhancedSuggestions(enhancedCarInfo);
            const finalConfidence = this.calculateFinalConfidence(recognition.confidence, enhancedCarInfo);
            const result = {
                carInfo: {
                    ...enhancedCarInfo,
                    confidence: finalConfidence,
                },
                suggestions: enhancedSuggestions,
                confidence: finalConfidence,
            };
            logger_1.logger.info(`Enhanced car recognition completed: ${result.carInfo.make} ${result.carInfo.model}`);
            return result;
        }
        catch (error) {
            logger_1.logger.error('Error in car recognition service:', error);
            throw new Error('Failed to recognize car from image');
        }
    }
    async enhanceCarInfo(carInfo) {
        try {
            const validatedInfo = validation_1.ValidationUtils.validateCarInfo(carInfo);
            const normalizedMake = this.normalizeMakeName(validatedInfo.make);
            const normalizedModel = this.normalizeModelName(validatedInfo.model);
            const dbInfo = this.lookupCarInDatabase(normalizedMake, normalizedModel, validatedInfo.year);
            const enhanced = {
                ...validatedInfo,
                make: normalizedMake,
                model: normalizedModel,
                generation: dbInfo?.generation || validatedInfo.generation,
                bodyType: dbInfo?.bodyType || validatedInfo.bodyType,
                detectedFeatures: [
                    ...validatedInfo.detectedFeatures,
                    ...(dbInfo?.commonFeatures || [])
                ].filter((feature, index, array) => array.indexOf(feature) === index),
            };
            return enhanced;
        }
        catch (error) {
            logger_1.logger.error('Error enhancing car info:', error);
            return carInfo;
        }
    }
    generateEnhancedSuggestions(carInfo) {
        const suggestions = [];
        const make = carInfo.make.toLowerCase();
        const bodyType = carInfo.bodyType.toLowerCase();
        const year = carInfo.year;
        suggestions.push('Sport wheels and tires');
        suggestions.push('Window tinting');
        if (bodyType.includes('sedan')) {
            suggestions.push('Rear spoiler');
            suggestions.push('Side skirts');
            suggestions.push('Lowering springs');
        }
        else if (bodyType.includes('hatchback')) {
            suggestions.push('Roof spoiler');
            suggestions.push('Front splitter');
            suggestions.push('Sport exhaust');
        }
        else if (bodyType.includes('suv') || bodyType.includes('crossover')) {
            suggestions.push('Running boards');
            suggestions.push('Roof rails');
            suggestions.push('Aggressive grille');
        }
        else if (bodyType.includes('coupe')) {
            suggestions.push('Body kit package');
            suggestions.push('Performance exhaust');
            suggestions.push('Carbon fiber accents');
        }
        if (make.includes('bmw')) {
            suggestions.push('M-Sport body kit');
            suggestions.push('Kidney grille upgrade');
        }
        else if (make.includes('audi')) {
            suggestions.push('S-Line body kit');
            suggestions.push('RS-style grille');
        }
        else if (make.includes('mercedes')) {
            suggestions.push('AMG body kit');
            suggestions.push('AMG wheels');
        }
        else if (make.includes('volkswagen') || make.includes('vw')) {
            suggestions.push('GTI-style body kit');
            suggestions.push('R-Line package');
        }
        else if (make.includes('honda')) {
            suggestions.push('Type R inspired modifications');
            suggestions.push('Mugen body kit');
        }
        else if (make.includes('toyota')) {
            suggestions.push('TRD body kit');
            suggestions.push('Sport package');
        }
        if (year >= 2020) {
            suggestions.push('LED light upgrades');
            suggestions.push('Digital dashboard enhancement');
        }
        else if (year >= 2010) {
            suggestions.push('Xenon headlight conversion');
            suggestions.push('Modern wheel designs');
        }
        return [...new Set(suggestions)].slice(0, 8);
    }
    calculateFinalConfidence(baseConfidence, carInfo) {
        let confidence = baseConfidence;
        const dbEntry = this.lookupCarInDatabase(carInfo.make, carInfo.model, carInfo.year);
        if (dbEntry) {
            confidence = Math.min(confidence + 0.1, 1.0);
        }
        const currentYear = new Date().getFullYear();
        if (carInfo.year < 2000 || carInfo.year > currentYear) {
            confidence = Math.max(confidence - 0.1, 0.0);
        }
        if (carInfo.detectedFeatures.length >= 5) {
            confidence = Math.min(confidence + 0.05, 1.0);
        }
        return Math.round(confidence * 100) / 100;
    }
    normalizeMakeName(make) {
        const makeMap = {
            'vw': 'Volkswagen',
            'bmw': 'BMW',
            'mb': 'Mercedes-Benz',
            'mercedes': 'Mercedes-Benz',
            'benz': 'Mercedes-Benz',
            'audi': 'Audi',
            'toyota': 'Toyota',
            'honda': 'Honda',
            'nissan': 'Nissan',
            'ford': 'Ford',
            'chevrolet': 'Chevrolet',
            'chevy': 'Chevrolet',
            'hyundai': 'Hyundai',
            'kia': 'Kia',
            'mazda': 'Mazda',
            'subaru': 'Subaru',
            'lexus': 'Lexus',
            'infiniti': 'Infiniti',
            'acura': 'Acura',
            'volvo': 'Volvo',
            'jaguar': 'Jaguar',
            'land rover': 'Land Rover',
            'porsche': 'Porsche',
            'ferrari': 'Ferrari',
            'lamborghini': 'Lamborghini',
            'maserati': 'Maserati',
            'bentley': 'Bentley',
            'rolls royce': 'Rolls-Royce',
        };
        const normalized = makeMap[make.toLowerCase()] || make;
        return normalized.charAt(0).toUpperCase() + normalized.slice(1);
    }
    normalizeModelName(model) {
        return model
            .replace(/\s+(sedan|hatchback|coupe|wagon|suv)$/i, '')
            .trim()
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');
    }
    lookupCarInDatabase(make, model, year) {
        const key = `${make.toLowerCase()}_${model.toLowerCase()}_${year}`;
        return this.carDatabase.get(key);
    }
    initializeCarDatabase() {
        const commonCars = [
            {
                make: 'BMW',
                model: '3 Series',
                years: [2019, 2020, 2021, 2022, 2023],
                bodyType: 'sedan',
                generation: 'G20',
                commonFeatures: ['kidney grille', 'angel eyes', 'hofmeister kink'],
            },
            {
                make: 'Audi',
                model: 'A4',
                years: [2017, 2018, 2019, 2020, 2021, 2022, 2023],
                bodyType: 'sedan',
                generation: 'B9',
                commonFeatures: ['quattro badge', 'singleframe grille', 'LED DRL'],
            },
            {
                make: 'Mercedes-Benz',
                model: 'C-Class',
                years: [2019, 2020, 2021, 2022, 2023],
                bodyType: 'sedan',
                generation: 'W205',
                commonFeatures: ['three-pointed star', 'LED headlights', 'chrome accents'],
            },
            {
                make: 'Volkswagen',
                model: 'Golf',
                years: [2020, 2021, 2022, 2023],
                bodyType: 'hatchback',
                generation: 'Mk8',
                commonFeatures: ['VW badge', 'C-pillar design', 'compact proportions'],
            },
            {
                make: 'Toyota',
                model: 'Camry',
                years: [2018, 2019, 2020, 2021, 2022, 2023],
                bodyType: 'sedan',
                generation: 'XV70',
                commonFeatures: ['aggressive front end', 'Toyota badge', 'LED headlights'],
            },
        ];
        commonCars.forEach(car => {
            car.years.forEach(year => {
                const key = `${car.make.toLowerCase()}_${car.model.toLowerCase()}_${year}`;
                this.carDatabase.set(key, {
                    generation: car.generation,
                    bodyType: car.bodyType,
                    commonFeatures: car.commonFeatures,
                });
            });
        });
        logger_1.logger.info(`Car database initialized with ${this.carDatabase.size} entries`);
    }
    async getCarSpecifications(carInfo) {
        try {
            const specs = this.lookupCarInDatabase(carInfo.make, carInfo.model, carInfo.year);
            if (!specs) {
                return {
                    wheelBoltPattern: 'Unknown',
                    suspensionType: 'Unknown',
                    engineType: 'Unknown',
                    transmissionType: 'Unknown',
                    driveType: 'Unknown',
                };
            }
            return specs;
        }
        catch (error) {
            logger_1.logger.error('Error getting car specifications:', error);
            return null;
        }
    }
}
exports.CarRecognitionService = CarRecognitionService;
//# sourceMappingURL=car-recognition.service.js.map