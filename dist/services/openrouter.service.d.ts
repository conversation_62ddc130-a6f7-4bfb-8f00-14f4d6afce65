import { CarInfo, CarRecognitionResponse } from '../types/index';
export declare class OpenRouterService {
    private client;
    constructor();
    recognizeCar(imageUrl: string): Promise<CarRecognitionResponse>;
    analyzeModifications(originalImageUrl: string, modifiedImageUrl: string, carInfo: CarInfo): Promise<string>;
    testConnection(): Promise<boolean>;
}
//# sourceMappingURL=openrouter.service.d.ts.map