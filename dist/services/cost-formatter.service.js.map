{"version": 3, "file": "cost-formatter.service.js", "sourceRoot": "", "sources": ["../../src/services/cost-formatter.service.ts"], "names": [], "mappings": ";;;AACA,2CAAwC;AAKxC,MAAa,oBAAoB;IAK/B,mBAAmB,CACjB,oBAA2C,EAC3C,SAAwB;QAExB,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAC7D,IAAI,CAAC,wBAAwB,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,CAAC,CAC9C,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEf,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAClD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YAEjC,OAAO;;EAEX,cAAc;;EAEd,OAAO;;EAEP,KAAK,EAAE,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;QAChC,CAAC;IACH,CAAC;IAKO,wBAAwB,CAAC,YAAiC,EAAE,KAAa;QAC/E,MAAM,QAAQ,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC;QAE9D,OAAO,GAAG,KAAK,KAAK,QAAQ,CAAC,IAAI;cACvB,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;eAC/B,QAAQ,CAAC,KAAK;mBACV,QAAQ,CAAC,gBAAgB;iBAC3B,QAAQ,CAAC,YAAY,IAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,YAAY,CAAC;kBACvE,QAAQ,CAAC,YAAY;mBACpB,QAAQ,CAAC,QAAQ,EAAE,CAAC;IACrC,CAAC;IAKO,iBAAiB,CAAC,SAAwB;QAChD,OAAO;eACI,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC;aACnC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC;sBACxB,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;IAC1D,CAAC;IAKO,WAAW;QACjB,OAAO;;;mCAGwB,CAAC;IAClC,CAAC;IAKO,yBAAyB,CAAC,YAAiC;QACjE,OAAO;YACL,IAAI,EAAE,YAAY,CAAC,YAAY;YAC/B,KAAK,EAAE,YAAY,CAAC,KAAK;YACzB,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,KAAK,CAAC;YAC/C,gBAAgB,EAAE,YAAY,CAAC,gBAAgB;YAC/C,YAAY,EAAE,IAAI,CAAC,qBAAqB,EAAE;YAC1C,YAAY,EAAE,IAAI,CAAC,qBAAqB,EAAE;YAC1C,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC5D,UAAU,EAAE,YAAY,CAAC,UAAU;YACnC,WAAW,EAAE,YAAY,CAAC,WAAW;SACtC,CAAC;IACJ,CAAC;IAKO,eAAe,CAAC,QAAgB;QACtC,MAAM,YAAY,GAAG,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,YAAY,CAAC,CAAC;IAC7C,CAAC;IAKO,WAAW,CAAC,KAAa;QAC/B,OAAO,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;IAKO,mBAAmB,CAAC,YAAoB;QAC9C,QAAQ,YAAY,EAAE,CAAC;YACrB,KAAK,WAAW;gBACd,OAAO,GAAG,CAAC;YACb,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC;YACd,KAAK,eAAe;gBAClB,OAAO,GAAG,CAAC;YACb;gBACE,OAAO,GAAG,CAAC;QACf,CAAC;IACH,CAAC;IAKO,qBAAqB;QAC3B,MAAM,QAAQ,GAAoD;YAChE,WAAW,EAAE,WAAW,EAAE,WAAW;YACrC,WAAW,EAAE,WAAW;YACxB,eAAe;SAChB,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IAC/D,CAAC;IAKO,qBAAqB;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAC3C,CAAC;IAKO,iBAAiB,CAAC,QAAgB;QACxC,MAAM,UAAU,GAA8B;YAC5C,UAAU,EAAE,YAAY;YACxB,QAAQ,EAAE,WAAW;YACrB,SAAS,EAAE,WAAW;YACtB,SAAS,EAAE,YAAY;YACvB,YAAY,EAAE,WAAW;YACzB,UAAU,EAAE,WAAW;YACvB,UAAU,EAAE,YAAY;YACxB,aAAa,EAAE,WAAW;SAC3B,CAAC;QAEF,OAAO,UAAU,CAAC,QAAQ,CAAC,IAAI,WAAW,CAAC;IAC7C,CAAC;IAKO,eAAe;QACrB,OAAO;;;;;;wDAM6C,CAAC;IACvD,CAAC;IAKD,uBAAuB,CAAC,SAAwB;QAC9C,OAAO,uBAAuB,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC;eACpD,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,gBAAgB,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;IACpG,CAAC;CACF;AA1KD,oDA0KC"}