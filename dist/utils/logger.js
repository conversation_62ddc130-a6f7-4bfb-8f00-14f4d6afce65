"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createModuleLogger = exports.logPerformance = exports.logError = exports.logRequest = exports.logger = void 0;
const winston_1 = __importDefault(require("winston"));
const index_1 = require("../config/index");
const logFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss',
}), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json(), winston_1.default.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    if (Object.keys(meta).length > 0) {
        try {
            log += ` ${JSON.stringify(meta, (key, value) => {
                if (typeof value === 'object' && value !== null) {
                    if (value.constructor && value.constructor.name === 'TLSSocket')
                        return '[TLSSocket]';
                    if (value.constructor && value.constructor.name === 'HTTPParser')
                        return '[HTTPParser]';
                    if (value.constructor && value.constructor.name === 'Socket')
                        return '[Socket]';
                    if (value.constructor && value.constructor.name === 'IncomingMessage')
                        return '[IncomingMessage]';
                }
                return value;
            })}`;
        }
        catch (error) {
            log += ` [Object with circular references]`;
        }
    }
    if (stack) {
        log += `\n${stack}`;
    }
    return log;
}));
exports.logger = winston_1.default.createLogger({
    level: index_1.loggingConfig.level,
    format: logFormat,
    defaultMeta: { service: 'car-modification-generator' },
    transports: [
        new winston_1.default.transports.Console({
            format: winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.simple()),
        }),
        new winston_1.default.transports.File({
            filename: index_1.loggingConfig.file,
            maxsize: 5242880,
            maxFiles: 5,
        }),
        new winston_1.default.transports.File({
            filename: index_1.loggingConfig.file.replace('.log', '.error.log'),
            level: 'error',
            maxsize: 5242880,
            maxFiles: 5,
        }),
    ],
});
const logRequest = (method, url, userId, duration) => {
    exports.logger.info('Request processed', {
        method,
        url,
        userId,
        duration: duration ? `${duration}ms` : undefined,
    });
};
exports.logRequest = logRequest;
const logError = (error, context) => {
    exports.logger.error(error.message, {
        stack: error.stack,
        ...context,
    });
};
exports.logError = logError;
const logPerformance = (operation, duration, metadata) => {
    exports.logger.info('Performance metric', {
        operation,
        duration: `${duration}ms`,
        ...metadata,
    });
};
exports.logPerformance = logPerformance;
const createModuleLogger = (module) => {
    return exports.logger.child({ module });
};
exports.createModuleLogger = createModuleLogger;
exports.default = exports.logger;
//# sourceMappingURL=logger.js.map