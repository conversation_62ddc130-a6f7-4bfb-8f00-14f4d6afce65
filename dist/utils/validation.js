"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationUtils = exports.InstallationComplexitySchema = exports.ModificationCategorySchema = exports.PricingSchema = exports.FileValidationSchema = exports.UserInputSchema = exports.ModificationRequestSchema = exports.CarInfoSchema = void 0;
const zod_1 = require("zod");
const types_1 = require("@/types");
exports.CarInfoSchema = zod_1.z.object({
    id: zod_1.z.string(),
    make: zod_1.z.string().min(1, 'Car make is required'),
    model: zod_1.z.string().min(1, 'Car model is required'),
    year: zod_1.z.number().int().min(1900).max(new Date().getFullYear() + 2),
    generation: zod_1.z.string().optional(),
    bodyType: zod_1.z.string().min(1, 'Body type is required'),
    confidence: zod_1.z.number().min(0).max(1),
    detectedFeatures: zod_1.z.array(zod_1.z.string()),
});
exports.ModificationRequestSchema = zod_1.z.object({
    carInfo: exports.CarInfoSchema,
    requestedModifications: zod_1.z.array(zod_1.z.string().min(1)),
    userPreferences: zod_1.z.object({
        budget: zod_1.z.number().positive().optional(),
        style: zod_1.z.enum(['sport', 'luxury', 'aggressive', 'elegant']).optional(),
        priority: zod_1.z.enum(['visual', 'performance', 'balanced']).optional(),
    }).optional(),
});
exports.UserInputSchema = zod_1.z.object({
    text: zod_1.z.string().min(1).max(1000, 'Message too long'),
    userId: zod_1.z.number().int().positive(),
    chatId: zod_1.z.number().int(),
});
exports.FileValidationSchema = zod_1.z.object({
    fileId: zod_1.z.string().min(1),
    fileSize: zod_1.z.number().int().positive().max(10 * 1024 * 1024),
    mimeType: zod_1.z.string().regex(/^image\/(jpeg|jpg|png|webp)$/),
});
exports.PricingSchema = zod_1.z.object({
    specificPart: zod_1.z.string().min(1),
    brand: zod_1.z.string().min(1),
    partNumber: zod_1.z.string().optional(),
    price: zod_1.z.number().positive(),
    averagePrice: zod_1.z.number().positive(),
    installationTime: zod_1.z.string().min(1),
    description: zod_1.z.string().min(1),
});
exports.ModificationCategorySchema = zod_1.z.nativeEnum(types_1.ModificationCategory);
exports.InstallationComplexitySchema = zod_1.z.nativeEnum(types_1.InstallationComplexity);
class ValidationUtils {
    static validateCarInfo(data) {
        return exports.CarInfoSchema.parse(data);
    }
    static validateModificationRequest(data) {
        return exports.ModificationRequestSchema.parse(data);
    }
    static validateUserInput(data) {
        return exports.UserInputSchema.parse(data);
    }
    static validateFile(data) {
        return exports.FileValidationSchema.parse(data);
    }
    static validatePricing(data) {
        return exports.PricingSchema.parse(data);
    }
    static sanitizeText(text) {
        return text
            .trim()
            .replace(/[<>]/g, '')
            .substring(0, 1000);
    }
    static isValidImageUrl(url) {
        try {
            const urlObj = new URL(url);
            return ['http:', 'https:'].includes(urlObj.protocol);
        }
        catch {
            return false;
        }
    }
    static validateModificationText(text) {
        const sanitized = this.sanitizeText(text);
        if (sanitized.length < 3) {
            return false;
        }
        const keywords = [
            'body kit', 'bodykit', 'wheels', 'rims', 'spoiler', 'wing',
            'suspension', 'lower', 'exhaust', 'splitter', 'skirts',
            'aggressive', 'sport', 'racing', 'performance', 'tuning'
        ];
        const lowerText = sanitized.toLowerCase();
        return keywords.some(keyword => lowerText.includes(keyword));
    }
    static extractModificationKeywords(text) {
        const keywords = [
            'body kit', 'bodykit', 'front splitter', 'side skirts', 'rear diffuser',
            'wheels', 'rims', 'alloys', 'black wheels', 'chrome wheels',
            'spoiler', 'rear spoiler', 'wing', 'roof spoiler',
            'suspension', 'lowered', 'coilovers', 'springs',
            'exhaust', 'cat-back', 'muffler', 'pipes',
            'aggressive', 'sport', 'racing', 'performance'
        ];
        const lowerText = text.toLowerCase();
        return keywords.filter(keyword => lowerText.includes(keyword));
    }
    static validatePriceRange(min, max) {
        return min >= 0 && max >= min && max <= 50000;
    }
    static validateCarYear(year) {
        const currentYear = new Date().getFullYear();
        return year >= 1990 && year <= currentYear + 2;
    }
    static containsInappropriateContent(text) {
        const inappropriateWords = [
            'spam', 'scam', 'hack'
        ];
        const lowerText = text.toLowerCase();
        return inappropriateWords.some(word => lowerText.includes(word));
    }
    static validateSessionData(data) {
        try {
            return (typeof data.userId === 'number' &&
                typeof data.chatId === 'number' &&
                typeof data.currentStep === 'string' &&
                data.lastActivity instanceof Date);
        }
        catch {
            return false;
        }
    }
    static validateRateLimit(requestCount, timeWindow, maxRequests) {
        return requestCount <= maxRequests;
    }
}
exports.ValidationUtils = ValidationUtils;
//# sourceMappingURL=validation.js.map