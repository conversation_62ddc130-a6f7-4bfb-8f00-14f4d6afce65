"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImageUtils = void 0;
const sharp_1 = __importDefault(require("sharp"));
const axios_1 = __importDefault(require("axios"));
const logger_1 = require("./logger");
const config_1 = require("@/config");
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
class ImageUtils {
    static async downloadImage(imageUrl, filename) {
        try {
            const response = await axios_1.default.get(imageUrl, {
                responseType: 'arraybuffer',
                timeout: 30000,
            });
            const buffer = Buffer.from(response.data);
            const uploadDir = config_1.serverConfig.uploadDir;
            await promises_1.default.mkdir(uploadDir, { recursive: true });
            const filePath = path_1.default.join(uploadDir, filename);
            await promises_1.default.writeFile(filePath, buffer);
            logger_1.logger.info(`Image downloaded and saved: ${filePath}`);
            return filePath;
        }
        catch (error) {
            logger_1.logger.error('Error downloading image:', error);
            throw new Error('Failed to download image');
        }
    }
    static async resizeImage(inputPath, outputPath, width, height) {
        try {
            await (0, sharp_1.default)(inputPath)
                .resize(width, height, {
                fit: 'cover',
                position: 'center',
            })
                .jpeg({ quality: 90 })
                .toFile(outputPath);
            logger_1.logger.info(`Image resized: ${outputPath}`);
        }
        catch (error) {
            logger_1.logger.error('Error resizing image:', error);
            throw new Error('Failed to resize image');
        }
    }
    static async convertImage(inputPath, outputPath, format) {
        try {
            const sharpInstance = (0, sharp_1.default)(inputPath);
            switch (format) {
                case 'jpeg':
                    await sharpInstance.jpeg({ quality: 90 }).toFile(outputPath);
                    break;
                case 'png':
                    await sharpInstance.png({ quality: 90 }).toFile(outputPath);
                    break;
                case 'webp':
                    await sharpInstance.webp({ quality: 90 }).toFile(outputPath);
                    break;
            }
            logger_1.logger.info(`Image converted to ${format}: ${outputPath}`);
        }
        catch (error) {
            logger_1.logger.error('Error converting image:', error);
            throw new Error('Failed to convert image');
        }
    }
    static async getImageMetadata(imagePath) {
        try {
            const metadata = await (0, sharp_1.default)(imagePath).metadata();
            return metadata;
        }
        catch (error) {
            logger_1.logger.error('Error getting image metadata:', error);
            throw new Error('Failed to get image metadata');
        }
    }
    static async optimizeForWeb(inputPath, outputPath, maxWidth = 1024) {
        try {
            const metadata = await (0, sharp_1.default)(inputPath).metadata();
            const width = metadata.width || maxWidth;
            const height = metadata.height || Math.round((maxWidth * (metadata.height || 768)) / width);
            await (0, sharp_1.default)(inputPath)
                .resize(Math.min(width, maxWidth), Math.min(height, Math.round((maxWidth * height) / width)), {
                fit: 'inside',
                withoutEnlargement: true,
            })
                .jpeg({
                quality: 85,
                progressive: true,
            })
                .toFile(outputPath);
            logger_1.logger.info(`Image optimized for web: ${outputPath}`);
        }
        catch (error) {
            logger_1.logger.error('Error optimizing image:', error);
            throw new Error('Failed to optimize image');
        }
    }
    static async createThumbnail(inputPath, outputPath, size = 200) {
        try {
            await (0, sharp_1.default)(inputPath)
                .resize(size, size, {
                fit: 'cover',
                position: 'center',
            })
                .jpeg({ quality: 80 })
                .toFile(outputPath);
            logger_1.logger.info(`Thumbnail created: ${outputPath}`);
        }
        catch (error) {
            logger_1.logger.error('Error creating thumbnail:', error);
            throw new Error('Failed to create thumbnail');
        }
    }
    static async validateImage(imagePath) {
        try {
            const metadata = await (0, sharp_1.default)(imagePath).metadata();
            if (!metadata.width || !metadata.height) {
                return false;
            }
            const stats = await promises_1.default.stat(imagePath);
            if (stats.size > config_1.serverConfig.maxFileSize) {
                return false;
            }
            if (metadata.width > 4096 || metadata.height > 4096) {
                return false;
            }
            return true;
        }
        catch (error) {
            logger_1.logger.error('Error validating image:', error);
            return false;
        }
    }
    static async validateImageForEditing(imagePath) {
        const errors = [];
        try {
            const metadata = await (0, sharp_1.default)(imagePath).metadata();
            if (!metadata.width || !metadata.height) {
                errors.push('Invalid image dimensions');
            }
            const stats = await promises_1.default.stat(imagePath);
            if (stats.size > 4 * 1024 * 1024) {
                errors.push('Image too large for editing API (max 4MB)');
            }
            if (metadata.width && metadata.width < 256) {
                errors.push('Image width too small (minimum 256px)');
            }
            if (metadata.height && metadata.height < 256) {
                errors.push('Image height too small (minimum 256px)');
            }
            if (metadata.width && metadata.width > 1920) {
                errors.push('Image width too large for editing (maximum 1920px for Full HD support)');
            }
            if (metadata.height && metadata.height > 1080) {
                errors.push('Image height too large for editing (maximum 1080px for Full HD support)');
            }
            if (metadata.format && !['jpeg', 'jpg', 'png', 'webp'].includes(metadata.format)) {
                errors.push(`Unsupported format: ${metadata.format}. Use JPEG, PNG, or WebP`);
            }
            return {
                isValid: errors.length === 0,
                errors,
                metadata
            };
        }
        catch (error) {
            logger_1.logger.error('Error validating image for editing:', error);
            return {
                isValid: false,
                errors: ['Failed to read image file']
            };
        }
    }
    static async prepareForEditing(inputPath, outputPath, maxSize = 1920, maxFileSize = 4 * 1024 * 1024) {
        try {
            const metadata = await (0, sharp_1.default)(inputPath).metadata();
            const originalWidth = metadata.width || 1024;
            const originalHeight = metadata.height || 1024;
            const { targetWidth, targetHeight, resizeNeeded } = this.calculateOptimalDimensions(originalWidth, originalHeight, maxSize);
            let sharpInstance = (0, sharp_1.default)(inputPath);
            if (resizeNeeded) {
                sharpInstance = sharpInstance.resize(targetWidth, targetHeight, {
                    fit: 'inside',
                    withoutEnlargement: true,
                    background: { r: 255, g: 255, b: 255, alpha: 1 }
                });
                logger_1.logger.info(`Image resized from ${originalWidth}x${originalHeight} to ${targetWidth}x${targetHeight} (Full HD optimized)`);
            }
            else {
                logger_1.logger.info(`Preserving original resolution: ${originalWidth}x${originalHeight} (within Full HD bounds)`);
            }
            await sharpInstance
                .png({ quality: 95, compressionLevel: 6, progressive: true })
                .toFile(outputPath);
            const stats = await promises_1.default.stat(outputPath);
            if (stats.size > maxFileSize) {
                logger_1.logger.info(`File size ${stats.size} exceeds limit, compressing...`);
                await (0, sharp_1.default)(outputPath)
                    .png({ quality: 85, compressionLevel: 9, progressive: true })
                    .toFile(outputPath + '.tmp');
                await promises_1.default.rename(outputPath + '.tmp', outputPath);
                logger_1.logger.info('Image compressed to meet file size requirements');
            }
            logger_1.logger.info(`Image prepared for editing: ${outputPath} (${targetWidth}x${targetHeight})`);
        }
        catch (error) {
            logger_1.logger.error('Error preparing image for editing:', error);
            throw new Error('Failed to prepare image for editing');
        }
    }
    static calculateOptimalDimensions(originalWidth, originalHeight, maxSize = 1920) {
        const aspectRatio = originalWidth / originalHeight;
        const fullHDWidth = Math.min(maxSize, 1920);
        const fullHDHeight = 1080;
        const fullHDAspectRatio = fullHDWidth / fullHDHeight;
        let targetWidth = originalWidth;
        let targetHeight = originalHeight;
        let resizeNeeded = false;
        if (originalWidth > fullHDWidth || originalHeight > fullHDHeight) {
            resizeNeeded = true;
            if (aspectRatio > fullHDAspectRatio) {
                targetWidth = fullHDWidth;
                targetHeight = Math.round(fullHDWidth / aspectRatio);
            }
            else {
                targetHeight = fullHDHeight;
                targetWidth = Math.round(fullHDHeight * aspectRatio);
            }
        }
        else if (originalWidth < 512 && originalHeight < 512) {
            resizeNeeded = true;
            const minSize = 512;
            if (originalWidth < originalHeight) {
                targetWidth = minSize;
                targetHeight = Math.round(minSize / aspectRatio);
            }
            else {
                targetHeight = minSize;
                targetWidth = Math.round(minSize * aspectRatio);
            }
        }
        targetWidth = Math.round(targetWidth / 2) * 2;
        targetHeight = Math.round(targetHeight / 2) * 2;
        return { targetWidth, targetHeight, resizeNeeded };
    }
    static async downloadAndPrepareForEditing(imageUrl, outputPath, maxSize = 1920, maxFileSize = 4 * 1024 * 1024) {
        try {
            const response = await axios_1.default.get(imageUrl, {
                responseType: 'arraybuffer',
                timeout: 15000,
            });
            const originalBuffer = Buffer.from(response.data);
            const metadata = await (0, sharp_1.default)(originalBuffer).metadata();
            const originalWidth = metadata.width || 1024;
            const originalHeight = metadata.height || 1024;
            const { targetWidth, targetHeight, resizeNeeded } = this.calculateOptimalDimensions(originalWidth, originalHeight, maxSize);
            let sharpInstance = (0, sharp_1.default)(originalBuffer);
            if (resizeNeeded) {
                sharpInstance = sharpInstance.resize(targetWidth, targetHeight, {
                    fit: 'inside',
                    withoutEnlargement: true,
                    background: { r: 255, g: 255, b: 255, alpha: 1 }
                });
                logger_1.logger.info(`Downloaded image resized from ${originalWidth}x${originalHeight} to ${targetWidth}x${targetHeight} (Full HD optimized)`);
            }
            else {
                logger_1.logger.info(`Downloaded image preserving original resolution: ${originalWidth}x${originalHeight} (within Full HD bounds)`);
            }
            let processedBuffer = await sharpInstance
                .png({ quality: 95, compressionLevel: 6, progressive: true })
                .toBuffer();
            if (processedBuffer.length > maxFileSize) {
                logger_1.logger.info(`Downloaded image size ${processedBuffer.length} exceeds limit, compressing...`);
                processedBuffer = await (0, sharp_1.default)(processedBuffer)
                    .png({ quality: 85, compressionLevel: 9, progressive: true })
                    .toBuffer();
                logger_1.logger.info('Downloaded image compressed to meet file size requirements');
            }
            if (outputPath) {
                await promises_1.default.writeFile(outputPath, processedBuffer);
                logger_1.logger.info(`Image downloaded and prepared: ${outputPath} (${targetWidth}x${targetHeight})`);
            }
            return processedBuffer;
        }
        catch (error) {
            logger_1.logger.error('Error downloading and preparing image:', error);
            throw new Error('Failed to download and prepare image');
        }
    }
    static async cleanupOldFiles(directory, maxAgeHours = 24) {
        try {
            const files = await promises_1.default.readdir(directory);
            const now = Date.now();
            const maxAge = maxAgeHours * 60 * 60 * 1000;
            for (const file of files) {
                const filePath = path_1.default.join(directory, file);
                const stats = await promises_1.default.stat(filePath);
                if (now - stats.mtime.getTime() > maxAge) {
                    await promises_1.default.unlink(filePath);
                    logger_1.logger.info(`Cleaned up old file: ${filePath}`);
                }
            }
        }
        catch (error) {
            logger_1.logger.error('Error cleaning up old files:', error);
        }
    }
    static generateFilename(prefix = 'img', extension = 'jpg') {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        return `${prefix}_${timestamp}_${random}.${extension}`;
    }
    static async generateCarPartMask(imagePath, outputPath, partType) {
        try {
            const metadata = await (0, sharp_1.default)(imagePath).metadata();
            const width = metadata.width || 1024;
            const height = metadata.height || 1024;
            let maskBuffer;
            switch (partType) {
                case 'wheels':
                    maskBuffer = await (0, sharp_1.default)({
                        create: {
                            width,
                            height,
                            channels: 3,
                            background: { r: 0, g: 0, b: 0 }
                        }
                    })
                        .composite([
                        {
                            input: Buffer.from(`<svg width="${width}" height="${height}">
                <rect x="0" y="${Math.floor(height * 0.7)}" width="${width}" height="${Math.floor(height * 0.3)}" fill="white"/>
              </svg>`),
                            top: 0,
                            left: 0
                        }
                    ])
                        .png()
                        .toBuffer();
                    break;
                case 'body':
                    maskBuffer = await (0, sharp_1.default)({
                        create: {
                            width,
                            height,
                            channels: 3,
                            background: { r: 0, g: 0, b: 0 }
                        }
                    })
                        .composite([
                        {
                            input: Buffer.from(`<svg width="${width}" height="${height}">
                <rect x="${Math.floor(width * 0.1)}" y="${Math.floor(height * 0.2)}" width="${Math.floor(width * 0.8)}" height="${Math.floor(height * 0.6)}" fill="white"/>
              </svg>`),
                            top: 0,
                            left: 0
                        }
                    ])
                        .png()
                        .toBuffer();
                    break;
                case 'windows':
                    maskBuffer = await (0, sharp_1.default)({
                        create: {
                            width,
                            height,
                            channels: 3,
                            background: { r: 0, g: 0, b: 0 }
                        }
                    })
                        .composite([
                        {
                            input: Buffer.from(`<svg width="${width}" height="${height}">
                <rect x="${Math.floor(width * 0.2)}" y="0" width="${Math.floor(width * 0.6)}" height="${Math.floor(height * 0.5)}" fill="white"/>
              </svg>`),
                            top: 0,
                            left: 0
                        }
                    ])
                        .png()
                        .toBuffer();
                    break;
                case 'full':
                default:
                    maskBuffer = await (0, sharp_1.default)({
                        create: {
                            width,
                            height,
                            channels: 3,
                            background: { r: 255, g: 255, b: 255 }
                        }
                    })
                        .png()
                        .toBuffer();
                    break;
            }
            await promises_1.default.writeFile(outputPath, maskBuffer);
            logger_1.logger.info(`Generated ${partType} mask: ${outputPath}`);
        }
        catch (error) {
            logger_1.logger.error('Error generating car part mask:', error);
            throw new Error('Failed to generate mask');
        }
    }
    static detectModificationType(userRequest) {
        const request = userRequest.toLowerCase();
        if (request.includes('wheel') || request.includes('rim') || request.includes('tire') ||
            request.includes('диск') || request.includes('колес')) {
            return 'wheels';
        }
        if (request.includes('window') || request.includes('tint') || request.includes('glass') ||
            request.includes('окн') || request.includes('стекл') || request.includes('тонир')) {
            return 'windows';
        }
        if (request.includes('body') || request.includes('kit') || request.includes('bumper') ||
            request.includes('spoiler') || request.includes('обвес') || request.includes('бампер') ||
            request.includes('спойлер')) {
            return 'body';
        }
        return 'full';
    }
    static async validateImageQuality(imagePath) {
        try {
            const metadata = await (0, sharp_1.default)(imagePath).metadata();
            const stats = await (0, sharp_1.default)(imagePath).stats();
            let qualityScore = 100;
            const recommendations = [];
            const totalPixels = (metadata.width || 0) * (metadata.height || 0);
            if (totalPixels < 500000) {
                qualityScore -= 30;
                recommendations.push('Image resolution is low - consider using a higher resolution photo for better results');
            }
            const brightness = stats.channels?.[0]?.mean || 128;
            if (brightness < 50) {
                qualityScore -= 20;
                recommendations.push('Image appears too dark - modifications may not be clearly visible');
            }
            else if (brightness > 200) {
                qualityScore -= 15;
                recommendations.push('Image appears overexposed - may affect modification quality');
            }
            const stdDev = stats.channels?.[0]?.stdev || 50;
            if (stdDev < 20) {
                qualityScore -= 25;
                recommendations.push('Image has low contrast - modifications may not blend well');
            }
            if (metadata.format === 'jpeg' && metadata.density && metadata.density < 72) {
                qualityScore -= 10;
                recommendations.push('Image has low DPI - may affect detail quality');
            }
            const isHighQuality = qualityScore >= 70;
            return {
                isHighQuality,
                qualityScore,
                recommendations
            };
        }
        catch (error) {
            logger_1.logger.error('Error validating image quality:', error);
            return {
                isHighQuality: false,
                qualityScore: 0,
                recommendations: ['Unable to analyze image quality']
            };
        }
    }
    static async enhanceImageForModification(inputPath, outputPath) {
        try {
            const stats = await (0, sharp_1.default)(inputPath).stats();
            let sharpInstance = (0, sharp_1.default)(inputPath);
            const brightness = stats.channels?.[0]?.mean || 128;
            const contrast = stats.channels?.[0]?.stdev || 50;
            if (brightness < 80) {
                sharpInstance = sharpInstance.modulate({ brightness: 1.2 });
                logger_1.logger.info('Applied brightness enhancement');
            }
            else if (brightness > 180) {
                sharpInstance = sharpInstance.modulate({ brightness: 0.9 });
                logger_1.logger.info('Applied brightness reduction');
            }
            if (contrast < 30) {
                sharpInstance = sharpInstance.modulate({ saturation: 1.1 });
                logger_1.logger.info('Applied saturation enhancement for better contrast');
            }
            sharpInstance = sharpInstance.sharpen({ sigma: 0.5, m1: 0.5, m2: 2 });
            await sharpInstance
                .png({ quality: 95 })
                .toFile(outputPath);
            logger_1.logger.info(`Image enhanced for modification: ${outputPath}`);
        }
        catch (error) {
            logger_1.logger.error('Error enhancing image:', error);
            throw new Error('Failed to enhance image for modification');
        }
    }
}
exports.ImageUtils = ImageUtils;
//# sourceMappingURL=image.utils.js.map