export declare class APITestUtils {
    private proxyAPIService;
    private openRouterService;
    constructor();
    testAllConnections(): Promise<{
        proxyAPI: boolean;
        openRouter: boolean;
        overall: boolean;
    }>;
    testProxyAPI(): Promise<boolean>;
    testOpenRouter(): Promise<boolean>;
    testCarRecognition(imageUrl: string): Promise<boolean>;
    testImageGeneration(): Promise<boolean>;
    runComprehensiveTests(testImageUrl?: string): Promise<{
        connections: {
            proxyAPI: boolean;
            openRouter: boolean;
            overall: boolean;
        };
        carRecognition?: boolean;
        imageGeneration?: boolean;
        overallSuccess: boolean;
    }>;
}
export declare const apiTestUtils: APITestUtils;
//# sourceMappingURL=api-test.d.ts.map