"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationStep = exports.InstallationComplexity = exports.ModificationCategory = void 0;
var ModificationCategory;
(function (ModificationCategory) {
    ModificationCategory["BODY_KIT"] = "body_kit";
    ModificationCategory["WHEELS"] = "wheels";
    ModificationCategory["SPOILER"] = "spoiler";
    ModificationCategory["EXHAUST"] = "exhaust";
    ModificationCategory["SUSPENSION"] = "suspension";
    ModificationCategory["INTERIOR"] = "interior";
    ModificationCategory["LIGHTING"] = "lighting";
    ModificationCategory["PERFORMANCE"] = "performance";
})(ModificationCategory || (exports.ModificationCategory = ModificationCategory = {}));
var InstallationComplexity;
(function (InstallationComplexity) {
    InstallationComplexity["EASY"] = "easy";
    InstallationComplexity["MEDIUM"] = "medium";
    InstallationComplexity["HARD"] = "hard";
    InstallationComplexity["PROFESSIONAL"] = "professional";
})(InstallationComplexity || (exports.InstallationComplexity = InstallationComplexity = {}));
var ConversationStep;
(function (ConversationStep) {
    ConversationStep["WAITING_FOR_IMAGE"] = "waiting_for_image";
    ConversationStep["PROCESSING_IMAGE"] = "processing_image";
    ConversationStep["CAR_RECOGNIZED"] = "car_recognized";
    ConversationStep["WAITING_FOR_MODIFICATIONS"] = "waiting_for_modifications";
    ConversationStep["GENERATING_MODIFICATIONS"] = "generating_modifications";
    ConversationStep["SHOWING_RESULTS"] = "showing_results";
})(ConversationStep || (exports.ConversationStep = ConversationStep = {}));
//# sourceMappingURL=index.js.map