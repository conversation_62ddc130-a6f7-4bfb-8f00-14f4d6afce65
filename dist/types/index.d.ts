export interface CarInfo {
    id: string;
    make: string;
    model: string;
    year: number;
    generation?: string;
    bodyType: string;
    confidence: number;
    detectedFeatures: string[];
}
export interface ModificationType {
    id: string;
    name: string;
    category: ModificationCategory;
    description: string;
    averagePrice: number;
    installationComplexity: InstallationComplexity;
    compatibleWith: string[];
}
export declare enum ModificationCategory {
    BODY_KIT = "body_kit",
    WHEELS = "wheels",
    SPOILER = "spoiler",
    EXHAUST = "exhaust",
    SUSPENSION = "suspension",
    INTERIOR = "interior",
    LIGHTING = "lighting",
    PERFORMANCE = "performance"
}
export declare enum InstallationComplexity {
    EASY = "easy",
    MEDIUM = "medium",
    HARD = "hard",
    PROFESSIONAL = "professional"
}
export interface ModificationRequest {
    carInfo: CarInfo;
    requestedModifications: string[];
    userPreferences?: {
        budget?: number;
        style?: 'sport' | 'luxury' | 'aggressive' | 'elegant';
        priority?: 'visual' | 'performance' | 'balanced';
    };
}
export interface ModificationResult {
    id: string;
    originalImageUrl: string;
    modifiedImageUrl: string;
    appliedModifications: AppliedModification[];
    totalCost: CostBreakdown;
    description: string;
    installationNotes: string;
}
export interface AppliedModification {
    type: ModificationType;
    specificPart: string;
    brand: string;
    partNumber?: string;
    price: number;
    installationTime: string;
    description: string;
}
export interface CostBreakdown {
    parts: number;
    labor: number;
    total: number;
    currency: string;
    priceRange: {
        min: number;
        max: number;
    };
}
export interface DetailedPartInfo {
    name: string;
    brand: string;
    price: number;
    installationTime: string;
    availability: 'В наличии' | 'Под заказ' | 'Нет в наличии';
    deliveryDays: number;
    warranty: string;
    partNumber?: string;
    description?: string;
}
export interface TelegramUser {
    id: number;
    username?: string;
    firstName: string;
    lastName?: string;
    languageCode?: string;
}
export interface UserSession {
    userId: number;
    chatId: number;
    currentStep: ConversationStep;
    uploadedImage?: string;
    carInfo?: CarInfo;
    modificationRequest?: ModificationRequest;
    lastActivity: Date;
}
export declare enum ConversationStep {
    WAITING_FOR_IMAGE = "waiting_for_image",
    PROCESSING_IMAGE = "processing_image",
    CAR_RECOGNIZED = "car_recognized",
    WAITING_FOR_MODIFICATIONS = "waiting_for_modifications",
    GENERATING_MODIFICATIONS = "generating_modifications",
    SHOWING_RESULTS = "showing_results"
}
export interface APIResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
    timestamp: Date;
}
export interface OpenAIImageResponse {
    url: string;
    revisedPrompt?: string;
    modificationOverlay?: {
        modifications: string[];
        description: string;
        carInfo: string;
        textComparison?: string;
    };
}
export interface CarRecognitionResponse {
    carInfo: CarInfo;
    suggestions: string[];
    confidence: number;
}
//# sourceMappingURL=index.d.ts.map