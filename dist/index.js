"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
require("module-alias/register");
const config_1 = require("@/config");
const telegram_service_1 = require("@/services/telegram.service");
const logger_1 = require("@/utils/logger");
class CarModificationBot {
    constructor() {
        this.telegramService = new telegram_service_1.TelegramService();
    }
    async start() {
        try {
            logger_1.logger.info('Starting Car Modification Generator Bot...');
            logger_1.logger.info(`Environment: ${config_1.config.NODE_ENV}`);
            logger_1.logger.info(`Log level: ${config_1.config.LOG_LEVEL}`);
            this.telegramService.start();
            logger_1.logger.info('🚗 Car Modification Generator Bot started successfully!');
            logger_1.logger.info('Bot is ready to receive messages...');
        }
        catch (error) {
            logger_1.logger.error('Failed to start bot:', error);
            process.exit(1);
        }
    }
    async shutdown() {
        try {
            logger_1.logger.info('Shutting down Car Modification Generator Bot...');
            this.telegramService.stop();
            logger_1.logger.info('Bot shutdown completed');
            process.exit(0);
        }
        catch (error) {
            logger_1.logger.error('Error during shutdown:', error);
            process.exit(1);
        }
    }
}
const bot = new CarModificationBot();
process.on('SIGINT', () => {
    logger_1.logger.info('Received SIGINT signal');
    bot.shutdown();
});
process.on('SIGTERM', () => {
    logger_1.logger.info('Received SIGTERM signal');
    bot.shutdown();
});
process.on('unhandledRejection', (reason, promise) => {
    logger_1.logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
process.on('uncaughtException', (error) => {
    logger_1.logger.error('Uncaught Exception:', error);
    bot.shutdown();
});
bot.start().catch((error) => {
    logger_1.logger.error('Failed to start application:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map