#!/usr/bin/env python3
"""
Простой клиент для тестирования vLLM сервера
"""

import requests
import json
import time
import argparse

def test_server(base_url="http://localhost:8000"):
    """Тестирование vLLM сервера"""
    
    print(f"🔍 Тестирование сервера: {base_url}")
    
    # Проверка здоровья сервера
    try:
        health_response = requests.get(f"{base_url}/health", timeout=5)
        if health_response.status_code == 200:
            print("✅ Сервер работает!")
        else:
            print(f"⚠️  Сервер отвечает с кодом: {health_response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Сервер недоступен: {e}")
        return False
    
    # Тестовые запросы
    test_prompts = [
        "Привет! Как дела?",
        "Объясни простыми словами, что такое машинное обучение",
        "Напиши короткий Python код для вычисления факториала"
    ]
    
    print(f"\n💬 Тестирование генерации текста...")
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n📝 Тест {i}: {prompt}")
        
        # Данные для запроса
        data = {
            "model": "default",  # vLLM автоматически использует загруженную модель
            "prompt": prompt,
            "max_tokens": 150,
            "temperature": 0.7,
            "top_p": 0.9,
            "stream": False
        }
        
        try:
            start_time = time.time()
            
            response = requests.post(
                f"{base_url}/v1/completions",
                headers={"Content-Type": "application/json"},
                json=data,
                timeout=30
            )
            
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                generated_text = result["choices"][0]["text"]
                
                print(f"🤖 Ответ: {generated_text}")
                print(f"⏱️  Время: {end_time - start_time:.2f} сек")
                
                # Статистика
                if "usage" in result:
                    usage = result["usage"]
                    print(f"📊 Токены: {usage.get('total_tokens', 'N/A')}")
            else:
                print(f"❌ Ошибка: {response.status_code}")
                print(f"📄 Ответ: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Ошибка запроса: {e}")
    
    return True

def interactive_chat(base_url="http://localhost:8000"):
    """Интерактивный чат с моделью"""
    
    print(f"\n💬 Интерактивный чат с моделью")
    print(f"🌐 Сервер: {base_url}")
    print("💡 Введите 'quit' для выхода")
    print("=" * 40)
    
    while True:
        try:
            user_input = input("\n👤 Вы: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'выход']:
                print("👋 До свидания!")
                break
            
            if not user_input:
                continue
            
            # Данные для запроса
            data = {
                "model": "default",
                "prompt": user_input,
                "max_tokens": 200,
                "temperature": 0.7,
                "top_p": 0.9,
                "stream": False
            }
            
            print("🤖 Модель думает...")
            start_time = time.time()
            
            response = requests.post(
                f"{base_url}/v1/completions",
                headers={"Content-Type": "application/json"},
                json=data,
                timeout=30
            )
            
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                generated_text = result["choices"][0]["text"]
                
                print(f"🤖 Модель: {generated_text}")
                print(f"⏱️  ({end_time - start_time:.2f} сек)")
            else:
                print(f"❌ Ошибка: {response.status_code}")
                print(f"📄 {response.text}")
                
        except KeyboardInterrupt:
            print("\n\n👋 До свидания!")
            break
        except requests.exceptions.RequestException as e:
            print(f"❌ Ошибка соединения: {e}")

def main():
    """Главная функция"""
    parser = argparse.ArgumentParser(description="Клиент для тестирования vLLM сервера")
    parser.add_argument(
        "--url", 
        default="http://localhost:8000",
        help="URL сервера (по умолчанию: http://localhost:8000)"
    )
    parser.add_argument(
        "--mode", 
        choices=["test", "chat"],
        default="test",
        help="Режим работы: test (тестирование) или chat (интерактивный чат)"
    )
    
    args = parser.parse_args()
    
    print("🎯 vLLM Клиент")
    print("=" * 20)
    
    if args.mode == "test":
        test_server(args.url)
    elif args.mode == "chat":
        interactive_chat(args.url)

if __name__ == "__main__":
    main()
