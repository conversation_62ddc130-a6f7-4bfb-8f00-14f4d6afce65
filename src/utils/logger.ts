import winston from 'winston';
import { loggingConfig } from '../config/index';

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss',
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;

    if (Object.keys(meta).length > 0) {
      try {
        // Safe JSON stringify that handles circular references
        log += ` ${JSON.stringify(meta, (key, value) => {
          if (typeof value === 'object' && value !== null) {
            // Skip circular references and complex objects
            if (value.constructor && value.constructor.name === 'TLSSocket') return '[TLSSocket]';
            if (value.constructor && value.constructor.name === 'HTTPParser') return '[HTTPParser]';
            if (value.constructor && value.constructor.name === 'Socket') return '[Socket]';
            if (value.constructor && value.constructor.name === 'IncomingMessage') return '[IncomingMessage]';
          }
          return value;
        })}`;
      } catch (error) {
        log += ` [Object with circular references]`;
      }
    }

    if (stack) {
      log += `\n${stack}`;
    }

    return log;
  })
);

// Create logger instance
export const logger = winston.createLogger({
  level: loggingConfig.level,
  format: logFormat,
  defaultMeta: { service: 'car-modification-generator' },
  transports: [
    // Console transport for development
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    }),
    
    // File transport for all logs
    new winston.transports.File({
      filename: loggingConfig.file,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    
    // Separate file for errors
    new winston.transports.File({
      filename: loggingConfig.file.replace('.log', '.error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
  ],
});

// Add request logging helper
export const logRequest = (
  method: string,
  url: string,
  userId?: number,
  duration?: number
) => {
  logger.info('Request processed', {
    method,
    url,
    userId,
    duration: duration ? `${duration}ms` : undefined,
  });
};

// Add error logging helper
export const logError = (
  error: Error,
  context?: Record<string, any>
) => {
  logger.error(error.message, {
    stack: error.stack,
    ...context,
  });
};

// Add performance logging helper
export const logPerformance = (
  operation: string,
  duration: number,
  metadata?: Record<string, any>
) => {
  logger.info('Performance metric', {
    operation,
    duration: `${duration}ms`,
    ...metadata,
  });
};

// Create child logger for specific modules
export const createModuleLogger = (module: string) => {
  return logger.child({ module });
};

export default logger;
