import { PrismaClient } from '@prisma/client';
import { logger } from '@/utils/logger';

/**
 * Prisma database service with connection management
 */
export class PrismaService {
  private static instance: PrismaService;
  private prisma: PrismaClient;

  private constructor() {
    this.prisma = new PrismaClient({
      log: [
        { level: 'query', emit: 'event' },
        { level: 'error', emit: 'stdout' },
        { level: 'info', emit: 'stdout' },
        { level: 'warn', emit: 'stdout' },
      ],
    });

    // Log database queries in development
    if (process.env.NODE_ENV === 'development') {
      // Note: Query logging disabled due to TypeScript issues
      // this.prisma.$on('query', (e) => {
      //   logger.debug('Database Query:', {
      //     query: e.query,
      //     params: e.params,
      //     duration: `${e.duration}ms`,
      //   });
      // });
    }
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): PrismaService {
    if (!PrismaService.instance) {
      PrismaService.instance = new PrismaService();
    }
    return PrismaService.instance;
  }

  /**
   * Get Prisma client
   */
  public getClient(): PrismaClient {
    return this.prisma;
  }

  /**
   * Connect to database
   */
  public async connect(): Promise<void> {
    try {
      await this.prisma.$connect();
      logger.info('Database connected successfully');
    } catch (error) {
      logger.error('Failed to connect to database:', error);
      throw error;
    }
  }

  /**
   * Disconnect from database
   */
  public async disconnect(): Promise<void> {
    try {
      await this.prisma.$disconnect();
      logger.info('Database disconnected successfully');
    } catch (error) {
      logger.error('Error disconnecting from database:', error);
      throw error;
    }
  }

  /**
   * Check database health
   */
  public async healthCheck(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      logger.error('Database health check failed:', error);
      return false;
    }
  }

  /**
   * Execute database transaction
   */
  public async transaction<T>(
    fn: (prisma: any) => Promise<T>
  ): Promise<T> {
    return await this.prisma.$transaction(fn as any) as T;
  }

  /**
   * Get database statistics
   */
  public async getStats(): Promise<any> {
    try {
      const [
        userCount,
        carCount,
        modificationCount,
        sessionCount,
      ] = await Promise.all([
        this.prisma.user.count(),
        this.prisma.car.count(),
        this.prisma.modificationPart.count(),
        this.prisma.userSession.count(),
      ]);

      return {
        users: userCount,
        cars: carCount,
        modifications: modificationCount,
        activeSessions: sessionCount,
      };
    } catch (error) {
      logger.error('Error getting database stats:', error);
      return null;
    }
  }

  /**
   * Clean up old data
   */
  public async cleanup(): Promise<void> {
    try {
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

      // Clean up old sessions
      const deletedSessions = await this.prisma.userSession.deleteMany({
        where: {
          lastActivity: {
            lt: oneWeekAgo,
          },
        },
      });

      // Clean up old API usage records (keep last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const deletedApiUsage = await this.prisma.apiUsage.deleteMany({
        where: {
          createdAt: {
            lt: thirtyDaysAgo,
          },
        },
      });

      logger.info('Database cleanup completed', {
        deletedSessions: deletedSessions.count,
        deletedApiUsage: deletedApiUsage.count,
      });
    } catch (error) {
      logger.error('Error during database cleanup:', error);
    }
  }
}

// Export singleton instance
export const prismaService = PrismaService.getInstance();
