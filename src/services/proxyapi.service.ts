import axios, { AxiosInstance } from 'axios';
import FormD<PERSON> from 'form-data';
import { proxyAPIConfig, imageConfig } from '../config/index';
import { CarInfo, OpenAIImageResponse } from '../types/index';
import { logger } from '../utils/logger';

/**
 * Service for ProxyAPI.ru - handles image editing using GPT-image-1 model
 * Uses /images/edits endpoint to preserve original photo and modify only specified parts
 */
export class ProxyAPIService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: proxyAPIConfig.baseURL,
      headers: {
        'Authorization': `Bearer ${proxyAPIConfig.apiKey}`,
      },
      timeout: 120000, // 2 minutes timeout for image editing
    });
  }

  /**
   * Edit user's original car image using ProxyAPI GPT-image-1 model
   * Uses /images/edits endpoint to preserve original photo and modify only specified parts
   * Implements retry mechanism for improved reliability
   */
  async generateModifiedImage(
    originalImageUrl: string,
    carInfo: CarInfo,
    modifications: string[],
    userRequest: string
  ): Promise<OpenAIImageResponse> {
    const maxRetries = 4;
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 1) {
          logger.info(`🔄 Retry attempt ${attempt}/${maxRetries} for image editing`);
          // Exponential backoff: wait 2^attempt seconds
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        } else {
          logger.info('Starting image editing with ProxyAPI GPT-image-1');
        }

      // Download the original image as buffer
      const imageBuffer = await this.downloadImageAsBuffer(originalImageUrl);

      // Determine optimal resolution based on original image
      const optimalSize = await this.determineOptimalResolution(imageBuffer);

      // Generate editing prompt
      const prompt = this.generateImageEditPrompt(carInfo, modifications, userRequest);

      // Create form data for the /images/edits endpoint (ProxyAPI format)
      const formData = new FormData();
      formData.append('model', proxyAPIConfig.models.image);
      // CRITICAL FIX: ProxyAPI expects 'image[]' not 'image' for edits endpoint
      formData.append('image[]', imageBuffer, {
        filename: 'car_image.png',
        contentType: 'image/png',
      });
      formData.append('prompt', prompt);
      formData.append('size', optimalSize);
      formData.append('quality', imageConfig.quality);
      formData.append('output_format', 'png'); // Use PNG format for better quality and transparency support

      // Add mask for precise targeting if applicable
      const modificationType = this.detectModificationType(userRequest);
      if (modificationType !== 'full') {
        try {
          const maskBuffer = await this.generateTargetingMask(imageBuffer, modificationType);
          if (maskBuffer) {
            // ProxyAPI expects 'mask' (not 'mask[]') for targeting masks
            formData.append('mask', maskBuffer, {
              filename: 'mask.png',
              contentType: 'image/png',
            });
            logger.info(`Added ${modificationType} targeting mask for precise editing`);
          }
        } catch (maskError) {
          logger.warn('Failed to generate targeting mask, proceeding without mask:', maskError);
          // Continue without mask - the prompt should still guide the editing
        }
      }

      // Enhanced logging for debugging
      logger.info('📝 Generated prompt (' + prompt.length + ' characters)');
      logger.info('🚀 Sending image edit request to ProxyAPI');
      logger.info('📊 Request parameters: model=' + proxyAPIConfig.models.image + ', size=' + optimalSize + ', quality=' + imageConfig.quality + ', output_format=png');
      logger.info('📏 Image buffer: ' + imageBuffer.length + ' bytes, Prompt: ' + prompt.length + ' chars');
      logger.info('⏱️ Sending request to ProxyAPI /images/edits endpoint...');

      // Make the API request to /images/edits
      const response = await this.client.post('/images/edits', formData, {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${proxyAPIConfig.apiKey}`,
        },
        timeout: 120000, // 2 minutes timeout
      });

      // Extract the edited image
      const base64Image = response.data.data[0]?.b64_json;
      if (!base64Image) {
        throw new Error('No edited image returned from API');
      }

      const imageUrl = `data:image/jpeg;base64,${base64Image}`;

      logger.info('✅ Successfully edited car image with ProxyAPI');

      return {
        url: imageUrl,
        revisedPrompt: response.data.data[0]?.revised_prompt,
      };

      } catch (error) {
        lastError = error;
        logger.error(`❌ Image editing failed (attempt ${attempt}/${maxRetries}):`, error);

        if (axios.isAxiosError(error)) {
          const status = error.response?.status;
          const errorData = error.response?.data;

          logger.error('ProxyAPI error details:', {
            status,
            data: errorData,
            url: '/images/edits',
            method: 'post',
            retryCount: attempt - 1
          });

        // Provide specific error messages based on status codes
        let errorMessage = 'Unable to edit your car image. ';

        switch (status) {
          case 400:
            errorMessage += 'Invalid request format detected. This is likely a temporary API issue. Please try again in a moment.';
            break;
          case 401:
            errorMessage += 'API authentication failed. Please check your ProxyAPI key configuration.';
            break;
          case 402:
            errorMessage += 'Insufficient balance in ProxyAPI account. Please top up your balance to continue using image editing features.';
            break;
          case 413:
            errorMessage += 'Image file is too large. Please use an image smaller than 25MB.';
            break;
          case 429:
            errorMessage += 'Too many requests. Please wait a moment and try again.';
            break;
          case 500:
            errorMessage += 'Server error. Please try again later.';
            break;
          default:
            errorMessage += 'Temporary service issue. Please try again or contact support if the problem persists.';
        }

          // For non-retryable errors (auth, balance, file size), don't retry
          if (status === 401 || status === 402 || status === 413) {
            throw new Error(errorMessage);
          }

          // For retryable errors, continue to next attempt if not last
          if (attempt === maxRetries) {
            throw new Error(errorMessage);
          }
        } else {
          // For non-HTTP errors, retry if not last attempt
          if (attempt === maxRetries) {
            throw error;
          }
        }
      }
    }

    // If all retries failed, throw the last error
    logger.error('All retry attempts failed for image editing');
    throw lastError || new Error('Unable to edit your car image after multiple attempts. Please try again later.');
  }

  /**
   * Fallback method: Generate new image using /images/generations endpoint
   */
  private async generateImageFromPrompt(
    carInfo: CarInfo,
    modifications: string[],
    userRequest: string
  ): Promise<OpenAIImageResponse> {
    try {
      logger.info('Generating new car image with ProxyAPI (fallback method)');

      const prompt = this.generateModificationPrompt(carInfo, modifications, userRequest);

      const response = await this.client.post('/images/generations', {
        model: proxyAPIConfig.models.image,
        prompt: prompt,
        size: '1024x1024',
        quality: 'high',
        output_format: 'png',
      });

      const base64Image = response.data.data[0]?.b64_json;
      if (!base64Image) {
        throw new Error('No image generated in fallback method');
      }

      const imageUrl = `data:image/png;base64,${base64Image}`;

      logger.info('✅ Successfully generated car image with ProxyAPI (fallback)');

      return {
        url: imageUrl,
        revisedPrompt: response.data.data[0]?.revised_prompt,
      };
    } catch (error) {
      logger.error('Fallback image generation also failed:', error);
      throw new Error(
        'Unable to generate modified car image. Please try uploading a different photo or simplifying your modification request.'
      );
    }
  }

  /**
   * Generate a detailed prompt for car modification (used by both editing and generation)
   */
  private generateModificationPrompt(
    carInfo: CarInfo,
    modifications: string[],
    userRequest: string
  ): string {
    const basePrompt = `Create a high-quality, photorealistic image of a modified ${carInfo.year} ${carInfo.make} ${carInfo.model}`;

    const modificationDetails = modifications.length > 0
      ? ` with the following modifications: ${modifications.join(', ')}`
      : '';

    const userDetails = userRequest
      ? ` User specifically requested: ${userRequest}`
      : '';

    const styleInstructions = `. The image should be:
- Photorealistic and high-quality
- Show the car from a 3/4 front angle
- Good lighting and professional photography style
- Clean background (preferably automotive showroom or clean street)
- All modifications should look professionally installed
- Maintain the original car's proportions and design language`;

    return basePrompt + modificationDetails + userDetails + styleInstructions;
  }

  /**
   * Determine optimal resolution based on original image dimensions
   */
  private async determineOptimalResolution(imageBuffer: Buffer): Promise<string> {
    try {
      const sharp = require('sharp');
      const metadata = await sharp(imageBuffer).metadata();
      const originalWidth = metadata.width || 1024;
      const originalHeight = metadata.height || 1024;
      const originalAspectRatio = originalWidth / originalHeight;

      logger.info(`Original image dimensions: ${originalWidth}x${originalHeight} (aspect ratio: ${originalAspectRatio.toFixed(2)})`);

      // Target aspect ratio for 1920x1024
      const targetAspectRatio = imageConfig.aspectRatio;

      // If original is close to target aspect ratio, use 1920x1024
      if (Math.abs(originalAspectRatio - targetAspectRatio) < 0.2) {
        logger.info('Using primary resolution: 1920x1024');
        return `${imageConfig.outputWidth}x${imageConfig.outputHeight}`;
      }

      // For wider images, use horizontal format
      if (originalAspectRatio > 1.5) {
        logger.info('Using horizontal format: 1536x1024');
        return '1536x1024';
      }

      // For taller images, use vertical format
      if (originalAspectRatio < 0.8) {
        logger.info('Using vertical format: 1024x1536');
        return '1024x1536';
      }

      // Default to square format for everything else
      logger.info('Using square format: 1024x1024');
      return '1024x1024';

    } catch (error) {
      logger.warn('Failed to determine optimal resolution, using default:', error);
      return `${imageConfig.outputWidth}x${imageConfig.outputHeight}`;
    }
  }

  /**
   * Download image from URL as buffer for image editing with enhanced reliability
   */
  private async downloadImageAsBuffer(imageUrl: string): Promise<Buffer> {
    const maxRetries = 5; // Increased from 3 to 5
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 1) {
          logger.info(`🔄 Retry attempt ${attempt}/${maxRetries} for image download`);
          // Enhanced exponential backoff with jitter
          const baseDelay = Math.pow(2, attempt - 1) * 1000;
          const jitter = Math.random() * 1000; // Add randomness to prevent thundering herd
          await new Promise(resolve => setTimeout(resolve, baseDelay + jitter));
        }

        logger.info(`📥 Downloading image from: ${imageUrl}`);

        // Enhanced axios configuration for better reliability
        const response = await axios.get(imageUrl, {
          responseType: 'arraybuffer',
          timeout: 90000, // Increased to 90 seconds timeout
          maxRedirects: 10, // Increased redirect limit
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Pragma': 'no-cache',
            'Sec-Fetch-Dest': 'image',
            'Sec-Fetch-Mode': 'no-cors',
            'Sec-Fetch-Site': 'cross-site'
          },
          // Enhanced error handling
          validateStatus: (status) => status < 500, // Don't throw on 4xx errors
          // Enhanced connection pooling with circuit breaker pattern
          httpAgent: new (require('http').Agent)({
            keepAlive: true,
            keepAliveMsecs: 30000,
            maxSockets: 15,
            maxFreeSockets: 10,
            timeout: 90000,
            freeSocketTimeout: 30000
          }),
          httpsAgent: new (require('https').Agent)({
            keepAlive: true,
            keepAliveMsecs: 30000,
            maxSockets: 15,
            maxFreeSockets: 10,
            timeout: 90000,
            freeSocketTimeout: 30000,
            rejectUnauthorized: false // Allow self-signed certificates for development
          })
        });

        // Validate response
        if (response.status >= 400) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        if (!response.data || response.data.length === 0) {
          throw new Error('Empty response data received');
        }

        const buffer = Buffer.from(response.data);
        logger.info(`Image downloaded successfully, size: ${buffer.length} bytes`);

        // Validate buffer is a valid image
        if (buffer.length < 100) { // Minimum viable image size
          throw new Error(`Downloaded file too small: ${buffer.length} bytes`);
        }

        // Process image to ensure API compatibility with enhanced error handling
        const sharp = require('sharp');
        try {
          const processedBuffer = await sharp(buffer)
            .png({ quality: 90, compressionLevel: 6 })
            .toBuffer();

          // Final validation
          if (processedBuffer.length === 0) {
            throw new Error('Image processing resulted in empty buffer');
          }

          return processedBuffer;
        } catch (sharpError) {
          logger.error('Sharp image processing failed:', sharpError);
          // Fallback: return original buffer if processing fails
          return buffer;
        }
      } catch (error: any) {
        lastError = error;

        // Enhanced error logging for network issues
        if (error?.code === 'ECONNRESET' || error?.code === 'ECONNABORTED' || error?.message?.includes('socket hang up')) {
          logger.error(`🌐 Network connection issue (attempt ${attempt}/${maxRetries}): ${error?.code || 'socket hang up'} - ${error?.message}`);
          logger.error(`📍 URL: ${imageUrl}`);
          if (attempt === maxRetries) {
            logger.error(`⏱️ All download attempts failed - this appears to be a persistent Telegram API connectivity issue`);
          }
        } else if (error?.response) {
          logger.error(`📡 HTTP error downloading image (attempt ${attempt}/${maxRetries}): ${error.response.status} - ${error.response.statusText}`);
          logger.error(`📍 URL: ${imageUrl}`);
          // Don't retry on 4xx errors (client errors)
          if (error.response.status >= 400 && error.response.status < 500) {
            throw new Error(`HTTP ${error.response.status}: ${error.response.statusText}`);
          }
        } else {
          logger.error(`❌ Unexpected error downloading image (attempt ${attempt}/${maxRetries}):`, error);
        }

        // If this was the last attempt, throw the error
        if (attempt === maxRetries) {
          throw new Error(`Failed to download image after ${maxRetries} attempts: ${error?.message || error?.code || 'Unknown error'}`);
        }
      }
    }

    // This should never be reached, but just in case
    throw lastError || new Error('Failed to download image after all retry attempts');
  }

  /**
   * Generate prompt for image editing with realistic modifications and enhanced color precision
   */
  private generateImageEditPrompt(
    carInfo: CarInfo,
    modifications: string[],
    userRequest: string
  ): string {
    const modificationDetails = modifications.length > 0
      ? modifications.join(', ')
      : userRequest;

    // Detect modification type for better prompting
    const modificationType = this.detectModificationType(userRequest);

    // Extract specific colors from user request for precise targeting
    const extractedColors = this.extractSpecificColors(userRequest);
    const colorEnforcementInstructions = this.generateColorEnforcementInstructions(extractedColors, modificationType);

    // Validate position and color preservation requirements
    const validation = this.validatePositionAndColorPreservation(userRequest, modificationType);
    if (validation.warnings.length > 0) {
      logger.info('🔒 Position/Color preservation active:', validation.warnings);
    }

    // Generate strict positioning and color preservation instructions
    const positionLockInstructions = this.generatePositionLockInstructions();
    const colorPreservationInstructions = this.generateColorPreservationInstructions(modificationType);

    // Generate natural integration and sharpness reduction instructions
    const naturalIntegrationInstructions = this.generateNaturalIntegrationInstructions(modificationType);

    let specificInstructions = '';

    switch (modificationType) {
      case 'wheels':
        specificInstructions = `CRITICAL: ONLY modify the wheels/rims/tires. DO NOT change:
        - Car body, paint, or original color
        - Bumpers, spoilers, or body kit
        - Windows or window tinting
        - Background, lighting, or environment
        - Car position, angle, or perspective
        - Any other car parts

        ${positionLockInstructions}
        ${colorPreservationInstructions}

        ${colorEnforcementInstructions}

        WHEEL MODIFICATION REQUIREMENTS:
        - ONLY change the wheel design, rim style, tire sidewall, or wheel color as specified
        - Apply the EXACT color specified in the user request
        - Ensure wheel modifications are realistic and automotive-grade
        - Maintain proper wheel proportions and fitment

        ${naturalIntegrationInstructions}`;
        break;
      case 'windows':
        specificInstructions = `CRITICAL: ONLY modify the windows (add tinting or change window appearance). DO NOT change:
        - Car body, paint, or original color
        - Wheels, rims, or tires
        - Bumpers, spoilers, or body kit
        - Background, lighting, or environment
        - Car position, angle, or perspective
        - Any other car parts

        ${positionLockInstructions}
        ${colorPreservationInstructions}

        ${naturalIntegrationInstructions}

        ONLY add window tinting or modify window appearance as requested.`;
        break;
      case 'body':
        specificInstructions = `CRITICAL: ONLY modify body parts (bumpers, spoilers, side skirts, body kit). DO NOT change:
        - Wheels, rims, or tires
        - Windows or window tinting
        - Car paint color or original vehicle color
        - Background, lighting, or environment
        - Car position, angle, or perspective

        ${positionLockInstructions}
        ${colorPreservationInstructions}

        ${colorEnforcementInstructions}

        BODY MODIFICATION REQUIREMENTS:
        - ONLY modify the body kit, bumpers, spoilers, or exterior body panels as requested
        - Apply the EXACT color specified for body parts if color is mentioned
        - Ensure body modifications are realistic and automotive-grade
        - Maintain proper fitment and proportions

        ${naturalIntegrationInstructions}`;
        break;
      case 'paint':
        specificInstructions = `PAINT COLOR MODIFICATION: Change the car's paint color as requested. DO NOT change:
        - Car position, angle, or perspective
        - Wheels, rims, or tires
        - Windows or window tinting
        - Body kit, bumpers, or spoilers
        - Background, lighting, or environment
        - Car's shape, size, or proportions

        ${positionLockInstructions}

        ${colorEnforcementInstructions}

        PAINT COLOR REQUIREMENTS:
        - ONLY change the paint color/finish while maintaining all other aspects of the vehicle
        - Apply the EXACT color specified in the user request
        - Ensure uniform color application across all painted surfaces
        - Maintain realistic automotive paint finish and quality

        ${naturalIntegrationInstructions}`;
        break;
      default:
        specificInstructions = `CRITICAL: Keep the background, lighting, car angle, and original paint color exactly the same. Only modify the specifically requested parts.

        ${positionLockInstructions}
        ${colorPreservationInstructions}

        ${colorEnforcementInstructions}

        GENERAL MODIFICATION REQUIREMENTS:
        - DO NOT make changes to parts not mentioned in the request
        - Apply the EXACT colors specified in the user request
        - Ensure all modifications are realistic and automotive-grade

        ${naturalIntegrationInstructions}`;
    }

    return `PRECISE EDITING TASK: Edit this ${carInfo.year} ${carInfo.make} ${carInfo.model} image by ONLY modifying: ${modificationDetails}.

${specificInstructions}

MANDATORY PRESERVATION REQUIREMENTS:
- PRESERVE EXACTLY: Car's position, angle, orientation, and spatial placement in the frame
- PRESERVE EXACTLY: Original vehicle paint color, hue, saturation, and finish (unless paint change requested)
- PRESERVE EXACTLY: Background, environment, lighting conditions, shadows, reflections
- PRESERVE EXACTLY: Camera angle, perspective, depth of field, composition, viewpoint
- PRESERVE EXACTLY: Original photo's color temperature, contrast, exposure, grain
- PRESERVE EXACTLY: All car parts NOT mentioned in the modification request
- PRESERVE EXACTLY: Vehicle's size, proportions, and dimensional relationships

MODIFICATION QUALITY REQUIREMENTS:
- Use ONLY realistic automotive parts that are commercially available
- Match original photo's lighting on new parts (shadows, highlights, reflections)
- Use realistic automotive materials with proper surface textures
- Maintain photographic realism - avoid any CGI or artificial appearance
- Integrate modifications seamlessly with existing car design

CRITICAL VALIDATION REQUIREMENTS:
- The car must remain in the EXACT same position and angle as the original image
- The vehicle's original paint color must be preserved unless explicitly requested to change
- No rotation, translation, perspective shift, or repositioning of the vehicle is allowed
- The car's spatial relationship to the camera and frame must remain identical

PRECISION REQUIREMENT:
The edited image must be identical to the original except for the specifically requested modification. A side-by-side comparison should show ONLY the requested change and nothing else. The car's position and original color must remain completely unchanged.

FINAL COLOR VALIDATION CHECKPOINT:
${extractedColors.length > 0 ? `- VERIFY: The modified parts are EXACTLY ${extractedColors[0].toUpperCase()} as requested
- CONFIRM: No other colors are present on the modified components
- VALIDATE: The ${extractedColors[0]} color is clearly visible and matches the user's specification
- REJECT: Any result where the modified parts are not ${extractedColors[0].toUpperCase()}` : '- Ensure any color specifications in the request are precisely followed'}

The user specifically requested these modifications: "${userRequest}" - deliver EXACTLY what was asked for.`;
  }

  /**
   * Extract specific colors from user request for precise targeting
   */
  private extractSpecificColors(userRequest: string): string[] {
    const request = userRequest.toLowerCase();
    const colors: string[] = [];

    // Define comprehensive color patterns with variations
    const colorPatterns = [
      // Basic colors
      { pattern: /\b(black|matte black|gloss black|satin black)\b/g, color: 'black' },
      { pattern: /\b(white|pearl white|arctic white|snow white)\b/g, color: 'white' },
      { pattern: /\b(silver|metallic silver|chrome silver|platinum silver)\b/g, color: 'silver' },
      { pattern: /\b(gray|grey|charcoal|gunmetal|dark gray|light gray)\b/g, color: 'gray' },
      { pattern: /\b(red|crimson|cherry red|deep red|bright red)\b/g, color: 'red' },
      { pattern: /\b(blue|navy blue|royal blue|electric blue|midnight blue)\b/g, color: 'blue' },
      { pattern: /\b(green|forest green|emerald|lime green|dark green)\b/g, color: 'green' },
      { pattern: /\b(yellow|golden yellow|bright yellow|lemon yellow)\b/g, color: 'yellow' },
      { pattern: /\b(orange|burnt orange|bright orange|copper)\b/g, color: 'orange' },
      { pattern: /\b(purple|violet|magenta|deep purple)\b/g, color: 'purple' },
      { pattern: /\b(brown|bronze|copper|chocolate brown)\b/g, color: 'brown' },
      { pattern: /\b(gold|golden|champagne|metallic gold)\b/g, color: 'gold' },

      // Automotive-specific colors
      { pattern: /\b(chrome|polished chrome|brushed chrome)\b/g, color: 'chrome' },
      { pattern: /\b(carbon fiber|carbon|matte carbon)\b/g, color: 'carbon fiber' },
      { pattern: /\b(titanium|brushed titanium|matte titanium)\b/g, color: 'titanium' },
      { pattern: /\b(anthracite|dark anthracite)\b/g, color: 'anthracite' },
      { pattern: /\b(pearl|pearlescent)\b/g, color: 'pearl' },
      { pattern: /\b(metallic|metal)\b/g, color: 'metallic' },
      { pattern: /\b(matte|satin|flat)\b/g, color: 'matte' }
    ];

    // Extract colors using patterns
    for (const { pattern } of colorPatterns) {
      const matches = request.match(pattern);
      if (matches) {
        // Add the full matched text for more precise color specification
        matches.forEach(match => {
          if (!colors.includes(match.trim())) {
            colors.push(match.trim());
          }
        });
      }
    }

    // Log extracted colors for debugging
    if (colors.length > 0) {
      logger.info(`🎨 Extracted specific colors: ${colors.join(', ')}`);
    }

    return colors;
  }

  /**
   * Generate color enforcement instructions for precise color matching
   */
  private generateColorEnforcementInstructions(extractedColors: string[], modificationType: string): string {
    if (extractedColors.length === 0) {
      return ''; // No specific colors to enforce
    }

    const primaryColor = extractedColors[0]; // Use the first/most prominent color
    const allColors = extractedColors.join(', ');

    let instructions = `CRITICAL COLOR ENFORCEMENT REQUIREMENTS:
        - The modified parts MUST be EXACTLY ${primaryColor.toUpperCase()} color
        - REJECT any other colors - ONLY use ${primaryColor.toUpperCase()}
        - Color specification: ${allColors}
        - Apply the exact color tone, saturation, and finish as specified
        - Ensure the ${primaryColor} color is clearly visible and dominant on the modified parts`;

    // Add modification-specific color instructions
    switch (modificationType) {
      case 'wheels':
        instructions += `
        - WHEELS MUST BE ${primaryColor.toUpperCase()} - no exceptions
        - Apply ${primaryColor} to rims, spokes, and wheel centers
        - Maintain realistic ${primaryColor} automotive finish
        - Ensure ${primaryColor} wheels contrast properly with tires`;
        break;
      case 'body':
        instructions += `
        - BODY PARTS MUST BE ${primaryColor.toUpperCase()} - no exceptions
        - Apply ${primaryColor} to all modified body components
        - Match ${primaryColor} finish to automotive standards
        - Ensure ${primaryColor} integrates with existing car design`;
        break;
      case 'paint':
        instructions += `
        - ENTIRE CAR PAINT MUST BE ${primaryColor.toUpperCase()}
        - Apply ${primaryColor} uniformly across all painted surfaces
        - Maintain realistic ${primaryColor} automotive paint finish
        - Ensure ${primaryColor} is the dominant vehicle color`;
        break;
    }

    instructions += `

        COLOR VALIDATION CHECKPOINT:
        - Before finalizing, verify that the modified parts are definitively ${primaryColor.toUpperCase()}
        - If any part appears to be a different color, CORRECT it to ${primaryColor.toUpperCase()}
        - The user specifically requested ${primaryColor} - this is NON-NEGOTIABLE`;

    return instructions;
  }

  /**
   * Generate natural integration and sharpness reduction instructions
   */
  private generateNaturalIntegrationInstructions(modificationType: string): string {
    return `NATURAL INTEGRATION AND SHARPNESS REDUCTION REQUIREMENTS:

CRITICAL SHARPNESS CONTROL:
- REDUCE artificial sharpness on modified car parts to match the original image's natural softness
- AVOID over-sharpening that makes modifications stand out unnaturally from the rest of the photograph
- Apply subtle edge softening to modified parts so they blend seamlessly with the original image
- Ensure modified parts have the SAME level of sharpness/softness as the rest of the car
- DO NOT make modified parts appear artificially crisp or digitally enhanced

SEAMLESS EDGE BLENDING:
- Apply natural edge blending at the boundaries between original and modified parts
- Soften transition edges to eliminate harsh lines that reveal digital modification
- Blend textures naturally where modified parts meet original surfaces
- Ensure no visible seams or sharp boundaries around modified areas
- Match the edge characteristics of the original photograph

PHOTOGRAPHIC CONSISTENCY:
- Modified parts must appear as if they were captured by the same camera with the same settings
- Match the original image's depth of field, focus, and blur characteristics
- Preserve the natural photographic grain and noise patterns on modified parts
- Ensure modified parts have the same compression artifacts as the original image
- Maintain consistent image quality and resolution across all areas

LIGHTING AND SURFACE INTEGRATION:
- Apply realistic lighting that matches the original photograph's conditions
- Ensure modified parts reflect light in the same way as the original car surfaces
- Match shadow softness and highlight characteristics of the original image
- Blend surface textures naturally with existing car materials
- Maintain consistent color temperature and lighting direction

MODIFICATION-SPECIFIC INTEGRATION:
${this.getModificationSpecificIntegrationInstructions(modificationType)}

FINAL INTEGRATION VALIDATION:
- The modified parts should be indistinguishable from the original photograph
- No artificial sharpness, over-enhancement, or digital artifacts should be visible
- The result should look like a single, naturally captured photograph
- Modified parts should NOT stand out as obviously edited or artificially added`;
  }

  /**
   * Get modification-specific integration instructions
   */
  private getModificationSpecificIntegrationInstructions(modificationType: string): string {
    switch (modificationType) {
      case 'wheels':
        return `WHEEL INTEGRATION SPECIFICS:
        - Ensure wheels have natural tire rubber texture without artificial sharpness
        - Apply realistic road dust, brake dust, or wear patterns to match the original wheels
        - Blend wheel well shadows naturally with the new wheel design
        - Match the original image's wheel focus and depth of field characteristics
        - Ensure spokes and rim details have natural, not artificially enhanced clarity`;

      case 'body':
        return `BODY KIT INTEGRATION SPECIFICS:
        - Blend body kit edges naturally with existing car panels
        - Match paint texture and surface characteristics of the original car
        - Apply realistic panel gap shadows and highlights
        - Ensure body modifications follow the car's natural curves without artificial enhancement
        - Match the original paint's level of gloss, reflection, and surface imperfections`;

      case 'paint':
        return `PAINT INTEGRATION SPECIFICS:
        - Apply paint with the same level of gloss and reflection as automotive paint
        - Match the original image's paint texture, orange peel, and surface characteristics
        - Ensure consistent paint depth and color saturation across all surfaces
        - Apply realistic clear coat reflections without artificial enhancement
        - Maintain natural paint imperfections and weathering patterns`;

      default:
        return `GENERAL INTEGRATION SPECIFICS:
        - Match the original image's overall sharpness and clarity levels
        - Apply realistic material properties without artificial enhancement
        - Ensure modifications blend naturally with surrounding car elements
        - Maintain photographic authenticity and natural appearance`;
    }
  }

  /**
   * Generate strict car position lock instructions
   */
  private generatePositionLockInstructions(): string {
    return `POSITION LOCK REQUIREMENTS:
        - Maintain the EXACT camera angle and perspective as the original image
        - Keep the car in the IDENTICAL position - no rotation, translation, or movement
        - Preserve the exact same viewpoint and framing as the source image
        - Do not change the car's orientation, tilt, or spatial positioning
        - Maintain the same distance and perspective relationship to the camera`;
  }

  /**
   * Generate color preservation instructions based on modification type
   */
  private generateColorPreservationInstructions(modificationType: string): string {
    if (modificationType === 'paint') {
      return ''; // No color preservation needed for paint modifications
    }

    return `COLOR PRESERVATION REQUIREMENTS:
        - Maintain the car's ORIGINAL paint color exactly as shown in the source image
        - Do not alter the hue, saturation, brightness, or tone of the vehicle's paint
        - Preserve the original color temperature and lighting on the car's surface
        - Do not apply any color filters, tints, or color shifts to the vehicle body
        - Keep the exact same paint finish (matte, gloss, metallic) as the original`;
  }

  /**
   * Detect modification type from user request with enhanced color change detection
   */
  private detectModificationType(userRequest: string): 'wheels' | 'body' | 'windows' | 'paint' | 'full' {
    const request = userRequest.toLowerCase();

    // Check for paint/color change requests first
    if (request.includes('paint') || request.includes('color') || request.includes('repaint') ||
        request.includes('change color') || request.includes('new color') || request.includes('different color')) {
      return 'paint';
    }

    if (request.includes('wheel') || request.includes('rim') || request.includes('tire')) {
      return 'wheels';
    }

    if (request.includes('window') || request.includes('tint')) {
      return 'windows';
    }

    if (request.includes('body') || request.includes('kit') || request.includes('bumper') ||
        request.includes('spoiler') || request.includes('splitter')) {
      return 'body';
    }

    return 'full';
  }

  /**
   * Validate that position and color preservation requirements are met
   */
  private validatePositionAndColorPreservation(
    userRequest: string,
    modificationType: string
  ): { isValid: boolean; warnings: string[] } {
    const warnings: string[] = [];
    let isValid = true;

    // Check for conflicting position change requests
    const positionKeywords = ['rotate', 'turn', 'angle', 'move', 'reposition', 'perspective', 'viewpoint'];
    const hasPositionConflict = positionKeywords.some(keyword =>
      userRequest.toLowerCase().includes(keyword)
    );

    if (hasPositionConflict && modificationType !== 'paint') {
      warnings.push('⚠️ Position change detected: The car will remain in its original position regardless of request');
    }

    // Check for conflicting color change requests (unless it's a paint modification)
    const colorKeywords = ['color', 'paint', 'repaint', 'hue', 'tint', 'shade'];
    const hasColorConflict = colorKeywords.some(keyword =>
      userRequest.toLowerCase().includes(keyword)
    ) && modificationType !== 'paint';

    if (hasColorConflict) {
      warnings.push('⚠️ Color change detected: The car\'s original paint color will be preserved unless specifically requesting a paint change');
    }

    // Log validation results
    if (warnings.length > 0) {
      logger.warn('Position/Color preservation validation warnings:', warnings);
    }

    return { isValid, warnings };
  }

  /**
   * Generate targeting mask for precise modification areas
   */
  private async generateTargetingMask(
    imageBuffer: Buffer,
    modificationType: 'wheels' | 'body' | 'windows' | 'paint'
  ): Promise<Buffer | null> {
    try {
      const sharp = require('sharp');
      const metadata = await sharp(imageBuffer).metadata();
      const width = metadata.width || 1024;
      const height = metadata.height || 1024;

      let maskSvg: string;

      switch (modificationType) {
        case 'wheels':
          // Create mask for wheel areas (lower corners and center-bottom)
          maskSvg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
            <rect width="${width}" height="${height}" fill="black"/>
            <!-- Front wheel area -->
            <circle cx="${Math.floor(width * 0.25)}" cy="${Math.floor(height * 0.75)}" r="${Math.floor(Math.min(width, height) * 0.08)}" fill="white"/>
            <!-- Rear wheel area -->
            <circle cx="${Math.floor(width * 0.75)}" cy="${Math.floor(height * 0.75)}" r="${Math.floor(Math.min(width, height) * 0.08)}" fill="white"/>
          </svg>`;
          break;

        case 'windows':
          // Create mask for window areas (upper portion of car)
          maskSvg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
            <rect width="${width}" height="${height}" fill="black"/>
            <!-- Main window area -->
            <rect x="${Math.floor(width * 0.15)}" y="${Math.floor(height * 0.1)}"
                  width="${Math.floor(width * 0.7)}" height="${Math.floor(height * 0.4)}" fill="white"/>
            <!-- Side windows -->
            <rect x="${Math.floor(width * 0.05)}" y="${Math.floor(height * 0.2)}"
                  width="${Math.floor(width * 0.15)}" height="${Math.floor(height * 0.25)}" fill="white"/>
            <rect x="${Math.floor(width * 0.8)}" y="${Math.floor(height * 0.2)}"
                  width="${Math.floor(width * 0.15)}" height="${Math.floor(height * 0.25)}" fill="white"/>
          </svg>`;
          break;

        case 'body':
          // Create mask for body areas (excluding wheels and windows)
          maskSvg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
            <rect width="${width}" height="${height}" fill="white"/>
            <!-- Exclude wheel areas -->
            <circle cx="${Math.floor(width * 0.25)}" cy="${Math.floor(height * 0.75)}" r="${Math.floor(Math.min(width, height) * 0.1)}" fill="black"/>
            <circle cx="${Math.floor(width * 0.75)}" cy="${Math.floor(height * 0.75)}" r="${Math.floor(Math.min(width, height) * 0.1)}" fill="black"/>
            <!-- Exclude main window area -->
            <rect x="${Math.floor(width * 0.15)}" y="0"
                  width="${Math.floor(width * 0.7)}" height="${Math.floor(height * 0.45)}" fill="black"/>
          </svg>`;
          break;

        case 'paint':
          // Create mask for paint areas (car body excluding wheels, windows, and trim)
          maskSvg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
            <rect width="${width}" height="${height}" fill="white"/>
            <!-- Exclude wheel areas -->
            <circle cx="${Math.floor(width * 0.25)}" cy="${Math.floor(height * 0.75)}" r="${Math.floor(Math.min(width, height) * 0.12)}" fill="black"/>
            <circle cx="${Math.floor(width * 0.75)}" cy="${Math.floor(height * 0.75)}" r="${Math.floor(Math.min(width, height) * 0.12)}" fill="black"/>
            <!-- Exclude window areas -->
            <rect x="${Math.floor(width * 0.15)}" y="0"
                  width="${Math.floor(width * 0.7)}" height="${Math.floor(height * 0.45)}" fill="black"/>
            <!-- Exclude side windows -->
            <rect x="${Math.floor(width * 0.05)}" y="${Math.floor(height * 0.2)}"
                  width="${Math.floor(width * 0.15)}" height="${Math.floor(height * 0.25)}" fill="black"/>
            <rect x="${Math.floor(width * 0.8)}" y="${Math.floor(height * 0.2)}"
                  width="${Math.floor(width * 0.15)}" height="${Math.floor(height * 0.25)}" fill="black"/>
          </svg>`;
          break;

        default:
          return null;
      }

      // Convert SVG to PNG buffer
      const maskBuffer = await sharp(Buffer.from(maskSvg))
        .png()
        .toBuffer();

      logger.info(`Generated ${modificationType} targeting mask (${width}x${height})`);
      return maskBuffer;

    } catch (error) {
      logger.error('Failed to generate targeting mask:', error);
      return null;
    }
  }

  /**
   * Test network connectivity to a URL
   */
  private async testNetworkConnectivity(url: string): Promise<boolean> {
    try {
      const response = await axios.head(url, {
        timeout: 10000,
        validateStatus: (status) => status < 500
      });
      return response.status < 400;
    } catch (error: any) {
      logger.warn(`Network connectivity test failed for ${url}:`, error?.code || error?.message);
      return false;
    }
  }

  /**
   * Enhanced download with network pre-check
   */
  private async downloadWithPreCheck(imageUrl: string): Promise<Buffer> {
    // First, test basic connectivity
    const isConnected = await this.testNetworkConnectivity(imageUrl);
    if (!isConnected) {
      logger.warn('⚠️ Network connectivity issue detected, but proceeding with download attempt...');
    }

    return this.downloadImageAsBuffer(imageUrl);
  }

  /**
   * Check ProxyAPI balance
   */
  async checkBalance(): Promise<{ balance: number; currency: string } | null> {
    try {
      const response = await this.client.get('/balance', {
        headers: {
          'Authorization': `Bearer ${proxyAPIConfig.apiKey}`,
        },
        timeout: 10000,
      });

      if (response.status === 200 && response.data) {
        logger.info(`💰 ProxyAPI Balance: ${response.data.balance} ${response.data.currency || 'RUB'}`);
        return {
          balance: response.data.balance || 0,
          currency: response.data.currency || 'RUB'
        };
      }
      return null;
    } catch (error) {
      logger.error('Failed to check ProxyAPI balance:', error);
      return null;
    }
  }

  /**
   * Test connection to ProxyAPI
   */
  async testConnection(): Promise<boolean> {
    try {
      // Test with a simple image editing request
      const testImageBuffer = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');

      const formData = new FormData();
      formData.append('model', proxyAPIConfig.models.image);
      formData.append('image', testImageBuffer, {
        filename: 'test.png',
        contentType: 'image/png',
      });
      formData.append('prompt', 'A simple test image');
      formData.append('size', '1024x1024');

      const response = await this.client.post('/images/edits', formData, {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${proxyAPIConfig.apiKey}`,
        },
        timeout: 30000, // 30 seconds for test
      });

      return response.status === 200 && response.data?.data?.[0]?.b64_json;
    } catch (error) {
      logger.error('ProxyAPI connection test failed:', error);
      return false;
    }
  }
}
