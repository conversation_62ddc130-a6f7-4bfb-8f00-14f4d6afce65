import { CarInfo } from '@/types';
import { logger } from '@/utils/logger';
import axios from 'axios';
import { config } from '@/config';
import { AbcpService, AbcpPartInfo } from './abcp.service';
import { ExistService, ExistPartInfo } from './exist.service';
import { PrismaClient } from '@prisma/client';

export interface ModificationPricing {
  specificPart: string;
  brand: string;
  partNumber?: string;
  price: number;
  averagePrice: number;
  installationTime: string;
  description: string;
  availability?: string;
  deliveryDays?: number;
  warranty?: string;
  material?: string;
  origin?: string;
}

/**
 * Service for getting modification pricing data
 * In production, this would integrate with real auto parts APIs
 */
export class PricingService {
  private priceDatabase: Map<string, any> = new Map();
  private abcpService: AbcpService;
  private existService: ExistService;
  private priceCache: Map<string, { data: any; timestamp: number }> = new Map();
  private readonly CACHE_TTL = 30 * 60 * 1000; // 30 minutes
  private prisma: PrismaClient;

  constructor() {
    this.initializePriceDatabase();
    this.abcpService = new AbcpService();
    this.existService = new ExistService();
    this.prisma = new PrismaClient();
  }

  /**
   * Get pricing for a specific modification with database integration
   */
  async getModificationPricing(
    carInfo: CarInfo,
    modification: string
  ): Promise<ModificationPricing> {
    try {
      // Сначала пытаемся найти в базе данных
      const dbPricing = await this.getPricingFromDatabase(carInfo, modification);
      if (dbPricing) {
        logger.debug(`Retrieved database pricing for ${modification}: ${dbPricing.price} ₽`);
        return dbPricing;
      }

      // Если не найдено в БД, используем внешние API
      const externalPricing = await this.getExternalModificationPricing(carInfo, modification);
      if (externalPricing) {
        logger.debug(`Retrieved external pricing for ${modification}: $${externalPricing.price}`);
        return externalPricing;
      }

      // Fallback к значениям по умолчанию
      logger.debug(`Using default pricing for ${modification}`);
      return this.getDefaultPricing(modification);
    } catch (error) {
      logger.error('Error getting modification pricing:', error);
      return this.getDefaultPricing(modification);
    }
  }

  /**
   * Generate a key for modification lookup
   */
  private generateModificationKey(carInfo: CarInfo, modification: string): string {
    const make = carInfo.make.toLowerCase();
    const model = carInfo.model.toLowerCase();
    const mod = modification.toLowerCase().replace(/\s+/g, '_');
    
    return `${make}_${model}_${mod}`;
  }

  /**
   * Get pricing from database
   */
  private async getPricingFromDatabase(
    carInfo: CarInfo,
    modification: string
  ): Promise<ModificationPricing | null> {
    try {
      // Ищем подходящую запчасть в базе данных
      const parts = await this.prisma.modificationPart.findMany({
        where: {
          OR: [
            { name: { contains: modification, mode: 'insensitive' } },
            { description: { contains: modification, mode: 'insensitive' } },
          ],
        },
        include: {
          category: true,
        },
        take: 5, // Берем первые 5 подходящих результатов
      });

      if (parts.length === 0) {
        return null;
      }

      // Выбираем наиболее подходящую запчасть
      const bestMatch = this.selectBestPartMatch(parts, modification);

      return {
        specificPart: bestMatch.name,
        brand: bestMatch.brand || 'OEM Style',
        partNumber: bestMatch.partNumber || undefined,
        price: Number(bestMatch.averagePrice), // USD цена для совместимости
        averagePrice: Number(bestMatch.averagePrice),
        installationTime: bestMatch.installationTime,
        description: bestMatch.description,
        availability: bestMatch.availability,
        deliveryDays: Math.floor((bestMatch.deliveryDaysMin + bestMatch.deliveryDaysMax) / 2),
        warranty: `${bestMatch.warrantyMonths} месяцев`,
        material: bestMatch.material || undefined,
        origin: bestMatch.origin,
      };
    } catch (error) {
      logger.error('Error getting pricing from database:', error);
      return null;
    }
  }

  /**
   * Select best matching part from database results
   */
  private selectBestPartMatch(parts: any[], modification: string): any {
    const modLower = modification.toLowerCase();

    // Сначала ищем точное совпадение по имени
    let bestMatch = parts.find(part =>
      part.name.toLowerCase().includes(modLower)
    );

    // Если не найдено, ищем по описанию
    if (!bestMatch) {
      bestMatch = parts.find(part =>
        part.description.toLowerCase().includes(modLower)
      );
    }

    // Если все еще не найдено, берем первый результат
    return bestMatch || parts[0];
  }

  /**
   * Get pricing from legacy internal database (fallback)
   */
  private getLegacyPricingFromDatabase(key: string, modification: string): ModificationPricing {
    const mod = modification.toLowerCase();
    
    // Body kit pricing
    if (mod.includes('body kit') || mod.includes('bodykit')) {
      return {
        specificPart: 'Complete Sport Body Kit',
        brand: 'Maxton Design',
        partNumber: 'BD-' + Math.random().toString(36).substr(2, 6).toUpperCase(),
        price: this.randomPrice(800, 2500),
        averagePrice: 1500,
        installationTime: '6-8 hours',
        description: 'Complete aerodynamic body kit including front splitter, side skirts, and rear diffuser',
      };
    }

    // Wheels pricing
    if (mod.includes('wheels') || mod.includes('rims')) {
      const isBlack = mod.includes('black');
      const basePrice = this.randomPrice(600, 1800);
      
      return {
        specificPart: isBlack ? 'Black Sport Alloy Wheels (Set of 4)' : 'Sport Alloy Wheels (Set of 4)',
        brand: 'BBS',
        partNumber: 'CH-R' + Math.random().toString(36).substr(2, 4).toUpperCase(),
        price: basePrice,
        averagePrice: 1200,
        installationTime: '1-2 hours',
        description: `High-quality ${isBlack ? 'matte black' : 'silver'} alloy wheels with performance tires`,
      };
    }

    // Spoiler pricing
    if (mod.includes('spoiler') || mod.includes('wing')) {
      return {
        specificPart: 'Rear Spoiler',
        brand: 'Vorsteiner',
        partNumber: 'SP-' + Math.random().toString(36).substr(2, 5).toUpperCase(),
        price: this.randomPrice(300, 800),
        averagePrice: 500,
        installationTime: '2-3 hours',
        description: 'Carbon fiber rear spoiler with aggressive styling',
      };
    }

    // Suspension pricing
    if (mod.includes('suspension') || mod.includes('lower')) {
      return {
        specificPart: 'Lowering Springs Kit',
        brand: 'Eibach',
        partNumber: 'PRO-KIT-' + Math.random().toString(36).substr(2, 4).toUpperCase(),
        price: this.randomPrice(400, 1200),
        averagePrice: 700,
        installationTime: '4-6 hours',
        description: 'Progressive rate lowering springs for improved handling and stance',
      };
    }

    // Exhaust pricing
    if (mod.includes('exhaust')) {
      return {
        specificPart: 'Cat-Back Exhaust System',
        brand: 'Borla',
        partNumber: 'ATAK-' + Math.random().toString(36).substr(2, 5).toUpperCase(),
        price: this.randomPrice(600, 1500),
        averagePrice: 900,
        installationTime: '2-4 hours',
        description: 'Stainless steel cat-back exhaust system with aggressive sound',
      };
    }

    // Front splitter pricing
    if (mod.includes('splitter')) {
      return {
        specificPart: 'Front Splitter',
        brand: 'APR Performance',
        partNumber: 'FS-' + Math.random().toString(36).substr(2, 6).toUpperCase(),
        price: this.randomPrice(200, 600),
        averagePrice: 350,
        installationTime: '1-2 hours',
        description: 'Carbon fiber front splitter for enhanced aerodynamics',
      };
    }

    // Default pricing for unknown modifications
    return this.getDefaultPricing(modification);
  }

  /**
   * Get default pricing for unknown modifications
   */
  private getDefaultPricing(modification: string): ModificationPricing {
    return {
      specificPart: this.formatModificationName(modification),
      brand: 'OEM Style',
      price: this.randomPrice(200, 800),
      averagePrice: 500,
      installationTime: '2-4 hours',
      description: `Quality aftermarket ${modification.toLowerCase()} modification`,
    };
  }

  /**
   * Format modification name
   */
  private formatModificationName(modification: string): string {
    return modification
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  /**
   * Generate random price within range
   */
  private randomPrice(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * Get pricing from external APIs (ABCP, Exist.ru)
   */
  private async getExternalModificationPricing(
    carInfo: CarInfo,
    modification: string
  ): Promise<ModificationPricing | null> {
    const cacheKey = `${carInfo.make}_${carInfo.model}_${modification}`;

    // Check cache first
    const cached = this.getCachedPrice(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      // Try ABCP first
      const abcpResult = await this.searchInABCP(carInfo, modification);
      if (abcpResult) {
        this.setCachedPrice(cacheKey, abcpResult);
        return abcpResult;
      }

      // Try Exist.ru as fallback
      const existResult = await this.searchInExist(carInfo, modification);
      if (existResult) {
        this.setCachedPrice(cacheKey, existResult);
        return existResult;
      }

      return null;
    } catch (error) {
      logger.error('Error getting external pricing:', error);
      return null;
    }
  }

  /**
   * Search for modification parts in ABCP
   */
  private async searchInABCP(
    carInfo: CarInfo,
    modification: string
  ): Promise<ModificationPricing | null> {
    try {
      const searchQuery = this.buildSearchQuery(carInfo, modification);
      const result = await this.abcpService.searchParts({
        query: searchQuery,
        limit: 5,
      });

      if (result.parts.length > 0) {
        const bestPart = this.selectBestPart(result.parts);
        return this.convertAbcpPartToPricing(bestPart, modification);
      }

      return null;
    } catch (error) {
      logger.error('Error searching in ABCP:', error);
      return null;
    }
  }

  /**
   * Search for modification parts in Exist.ru
   */
  private async searchInExist(
    carInfo: CarInfo,
    modification: string
  ): Promise<ModificationPricing | null> {
    try {
      const searchQuery = this.buildSearchQuery(carInfo, modification);
      const result = await this.existService.searchParts({
        query: searchQuery,
        limit: 5,
      });

      if (result.parts.length > 0) {
        const bestPart = this.selectBestExistPart(result.parts);
        return this.convertExistPartToPricing(bestPart, modification);
      }

      return null;
    } catch (error) {
      logger.error('Error searching in Exist.ru:', error);
      return null;
    }
  }

  /**
   * Build search query for external APIs
   */
  private buildSearchQuery(carInfo: CarInfo, modification: string): string {
    const mod = modification.toLowerCase();

    if (mod.includes('body kit')) {
      return `${carInfo.make} ${carInfo.model} body kit обвес`;
    }
    if (mod.includes('wheels') || mod.includes('rims')) {
      return `${carInfo.make} ${carInfo.model} диски колеса wheels`;
    }
    if (mod.includes('spoiler')) {
      return `${carInfo.make} ${carInfo.model} spoiler спойлер`;
    }
    if (mod.includes('suspension')) {
      return `${carInfo.make} ${carInfo.model} suspension подвеска`;
    }
    if (mod.includes('exhaust')) {
      return `${carInfo.make} ${carInfo.model} exhaust выхлоп`;
    }

    return `${carInfo.make} ${carInfo.model} ${modification}`;
  }

  /**
   * Select best part from ABCP results
   */
  private selectBestPart(parts: AbcpPartInfo[]): AbcpPartInfo {
    // Prefer parts with good availability and reasonable price
    return parts.sort((a, b) => {
      const scoreA = (a.availability > 0 ? 10 : 0) + (a.price < 10000 ? 5 : 0);
      const scoreB = (b.availability > 0 ? 10 : 0) + (b.price < 10000 ? 5 : 0);
      return scoreB - scoreA;
    })[0];
  }

  /**
   * Select best part from Exist.ru results
   */
  private selectBestExistPart(parts: ExistPartInfo[]): ExistPartInfo {
    // Prefer in-stock parts with reasonable price
    return parts.sort((a, b) => {
      const scoreA = (a.availability === 'in_stock' ? 10 : a.availability === 'order' ? 5 : 0);
      const scoreB = (b.availability === 'in_stock' ? 10 : b.availability === 'order' ? 5 : 0);
      return scoreB - scoreA;
    })[0];
  }

  /**
   * Convert ABCP part to ModificationPricing
   */
  private convertAbcpPartToPricing(part: AbcpPartInfo, modification: string): ModificationPricing {
    return {
      specificPart: part.name,
      brand: part.brand,
      partNumber: part.number,
      price: part.price,
      averagePrice: part.price,
      installationTime: this.estimateInstallationTime(modification),
      description: `${part.brand} ${part.name} - Available: ${part.availability} pcs, Delivery: ${part.deliveryDays} days`,
    };
  }

  /**
   * Convert Exist.ru part to ModificationPricing
   */
  private convertExistPartToPricing(part: ExistPartInfo, modification: string): ModificationPricing {
    return {
      specificPart: part.name,
      brand: part.brand,
      partNumber: part.partNumber,
      price: part.price,
      averagePrice: part.price,
      installationTime: this.estimateInstallationTime(modification),
      description: `${part.brand} ${part.name} - Status: ${part.availability}, Delivery: ${part.deliveryDays} days`,
    };
  }

  /**
   * Estimate installation time based on modification type
   */
  private estimateInstallationTime(modification: string): string {
    const mod = modification.toLowerCase();

    if (mod.includes('body kit')) return '6-8 hours';
    if (mod.includes('wheels')) return '1-2 hours';
    if (mod.includes('spoiler')) return '2-3 hours';
    if (mod.includes('suspension')) return '4-6 hours';
    if (mod.includes('exhaust')) return '2-4 hours';
    if (mod.includes('splitter')) return '1-2 hours';

    return '2-4 hours';
  }

  /**
   * Cache management methods
   */
  private getCachedPrice(key: string): ModificationPricing | null {
    const cached = this.priceCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.data;
    }
    return null;
  }

  private setCachedPrice(key: string, data: ModificationPricing): void {
    this.priceCache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  /**
   * Initialize price database with common modifications
   * In production, this would be loaded from external APIs or database
   */
  private initializePriceDatabase(): void {
    // This would be populated with real pricing data from auto parts suppliers
    logger.info('Price database initialized');
  }

  /**
   * Get pricing from external auto parts APIs
   * This is where you would integrate with real suppliers like:
   * - AutoDoc API
   * - Exist.ru API
   * - FCP Euro API
   * - Pelican Parts API
   */
  async getExternalPricing(
    carInfo: CarInfo,
    partNumber: string
  ): Promise<ModificationPricing | null> {
    try {
      // Try AutoDoc API if available
      if (config.AUTODOC_API_KEY) {
        const autodocPrice = await this.getAutodocPricing(carInfo, partNumber);
        if (autodocPrice) return autodocPrice;
      }

      // Try Exist.ru API if available
      if (config.EXIST_API_KEY) {
        const existPrice = await this.getExistPricing(carInfo, partNumber);
        if (existPrice) return existPrice;
      }

      // Fallback to internal pricing
      return null;
    } catch (error) {
      logger.error('Error fetching external pricing:', error);
      return null;
    }
  }

  /**
   * Get pricing from AutoDoc API
   */
  private async getAutodocPricing(
    carInfo: CarInfo,
    partNumber: string
  ): Promise<ModificationPricing | null> {
    try {
      // Example AutoDoc API integration
      const response = await axios.get(`https://api.autodoc.com/v1/parts/search`, {
        headers: {
          'Authorization': `Bearer ${config.AUTODOC_API_KEY}`,
          'Content-Type': 'application/json',
        },
        params: {
          make: carInfo.make,
          model: carInfo.model,
          year: carInfo.year,
          part_number: partNumber,
        },
        timeout: 5000,
      });

      if (response.data && response.data.parts && response.data.parts.length > 0) {
        const part = response.data.parts[0];
        return {
          specificPart: part.name,
          brand: part.brand,
          partNumber: part.part_number,
          price: parseFloat(part.price),
          averagePrice: parseFloat(part.price),
          installationTime: '2-4 hours',
          description: part.description || `${part.brand} ${part.name}`,
        };
      }

      return null;
    } catch (error) {
      logger.error('Error fetching AutoDoc pricing:', error);
      return null;
    }
  }

  /**
   * Get pricing from Exist.ru API
   */
  private async getExistPricing(
    carInfo: CarInfo,
    partNumber: string
  ): Promise<ModificationPricing | null> {
    try {
      // Example Exist.ru API integration
      const response = await axios.post('https://api.exist.ru/api/search', {
        login: 'your_login',
        password: 'your_password',
        search_string: partNumber,
        car_make: carInfo.make,
        car_model: carInfo.model,
        car_year: carInfo.year,
      }, {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 5000,
      });

      if (response.data && response.data.parts && response.data.parts.length > 0) {
        const part = response.data.parts[0];
        return {
          specificPart: part.name,
          brand: part.brand,
          partNumber: part.code,
          price: parseFloat(part.price),
          averagePrice: parseFloat(part.price),
          installationTime: '2-4 hours',
          description: part.description || `${part.brand} ${part.name}`,
        };
      }

      return null;
    } catch (error) {
      logger.error('Error fetching Exist.ru pricing:', error);
      return null;
    }
  }

  /**
   * Get price comparison from multiple suppliers
   */
  async getPriceComparison(
    carInfo: CarInfo,
    modification: string
  ): Promise<ModificationPricing[]> {
    // In production, this would query multiple suppliers
    const basePricing = await this.getModificationPricing(carInfo, modification);
    
    // Generate some price variations for comparison
    const variations: ModificationPricing[] = [
      basePricing,
      {
        ...basePricing,
        brand: 'OEM',
        price: Math.round(basePricing.price * 1.3),
        description: `OEM ${basePricing.specificPart}`,
      },
      {
        ...basePricing,
        brand: 'Budget Option',
        price: Math.round(basePricing.price * 0.7),
        description: `Budget ${basePricing.specificPart}`,
      },
    ];

    return variations;
  }
}
