import {
  CarInfo,
  ModificationResult,
  CarRecognitionResponse,
  AppliedModification,
  CostBreakdown,
  ModificationCategory,
  InstallationComplexity
} from '@/types';
import { OpenAIService } from './openai.service';
import { CarRecognitionService } from './car-recognition.service';
import { PricingService } from './pricing.service';
import { ValidationUtils } from '@/utils/validation';
import { logger } from '@/utils/logger';

export class ModificationService {
  private openAIService: OpenAIService;
  private carRecognitionService: CarRecognitionService;
  private pricingService: PricingService;

  constructor() {
    this.openAIService = new OpenAIService();
    this.carRecognitionService = new CarRecognitionService();
    this.pricingService = new PricingService();
  }

  /**
   * Recognize car from image using enhanced recognition service
   */
  async recognizeCar(imageUrl: string): Promise<CarRecognitionResponse> {
    return await this.carRecognitionService.recognizeCar(imageUrl);
  }

  /**
   * Generate car modifications based on user request
   */
  async generateModifications(
    originalImageUrl: string,
    carInfo: CarInfo,
    userRequest: string
  ): Promise<ModificationResult> {
    try {
      logger.info(`Generating modifications for ${carInfo.make} ${carInfo.model}`);

      // Parse user request to identify modification types
      const requestedModifications = this.parseModificationRequest(userRequest);

      // Generate modified image
      const modifiedImage = await this.openAIService.generateModifiedImage(
        originalImageUrl,
        carInfo,
        requestedModifications,
        userRequest
      );

      // Analyze what was actually applied (skip if images are the same)
      let analysisDescription: string;

      if (modifiedImage.url === originalImageUrl) {
        // If we're using the original image, generate description based on requested modifications
        analysisDescription = this.generateModificationDescription(carInfo, requestedModifications, userRequest);
      } else {
        // If we have a different image, analyze the differences
        analysisDescription = await this.openAIService.analyzeModifications(
          originalImageUrl,
          modifiedImage.url,
          carInfo
        );
      }

      // Генерируем детальную информацию о модификациях с ценами
      const appliedModifications = await this.generateAppliedModificationsList(
        carInfo,
        requestedModifications,
        analysisDescription
      );

      // Рассчитываем общую стоимость
      const totalCost = this.calculateTotalCost(appliedModifications);

      // Генерируем заметки по установке
      const installationNotes = this.generateInstallationNotes(appliedModifications);

      const result: ModificationResult = {
        id: `mod_${Date.now()}`,
        originalImageUrl,
        modifiedImageUrl: modifiedImage.url,
        appliedModifications,
        totalCost,
        description: analysisDescription,
        installationNotes,
      };

      logger.info('Modification generation completed successfully');
      return result;

    } catch (error) {
      logger.error('Error generating modifications:', error);
      throw new Error('Failed to generate modifications');
    }
  }

  /**
   * Generate modification description when using original image
   */
  private generateModificationDescription(
    carInfo: CarInfo,
    modifications: string[],
    userRequest: string
  ): string {
    const carDescription = `${carInfo.year} ${carInfo.make} ${carInfo.model}`;

    let description = `Proposed modifications for your ${carDescription}:\n\n`;

    if (modifications.length > 0) {
      description += "Recommended modifications:\n";
      modifications.forEach((mod, index) => {
        description += `${index + 1}. ${this.formatModificationName(mod)}\n`;
      });
    }

    if (userRequest) {
      description += `\nBased on your request: "${userRequest}"\n`;
    }

    description += `\nThese modifications would enhance the appearance and performance of your ${carDescription} while maintaining its original character and design language.`;

    return description;
  }

  /**
   * Parse user request to identify modification types with enhanced validation
   */
  private parseModificationRequest(userRequest: string): string[] {
    // Validate and sanitize input
    const sanitizedRequest = ValidationUtils.sanitizeText(userRequest);

    if (!ValidationUtils.validateModificationText(sanitizedRequest)) {
      logger.warn('Invalid modification request received:', userRequest);
      return ['sport modification package']; // Default fallback
    }

    // Extract keywords using validation utils
    const extractedKeywords = ValidationUtils.extractModificationKeywords(sanitizedRequest);

    if (extractedKeywords.length > 0) {
      return extractedKeywords;
    }

    // Fallback to manual parsing
    const request = sanitizedRequest.toLowerCase();
    const modifications: string[] = [];

    // Body kit related
    if (request.includes('body kit') || request.includes('bodykit')) {
      modifications.push('sport body kit');
    }
    if (request.includes('front splitter') || request.includes('splitter')) {
      modifications.push('front splitter');
    }
    if (request.includes('side skirts') || request.includes('skirts')) {
      modifications.push('side skirts');
    }

    // Wheels
    if (request.includes('wheels') || request.includes('rims') || request.includes('alloys')) {
      if (request.includes('black')) {
        modifications.push('black sport wheels');
      } else if (request.includes('chrome')) {
        modifications.push('chrome wheels');
      } else {
        modifications.push('sport wheels');
      }
    }

    // Spoilers
    if (request.includes('spoiler') || request.includes('wing')) {
      if (request.includes('rear')) {
        modifications.push('rear spoiler');
      } else {
        modifications.push('spoiler');
      }
    }

    // Suspension
    if (request.includes('lower') || request.includes('lowered') || request.includes('suspension')) {
      modifications.push('lowered suspension');
    }

    // Exhaust
    if (request.includes('exhaust') || request.includes('pipes')) {
      modifications.push('sport exhaust system');
    }

    // Style descriptors
    if (request.includes('aggressive') || request.includes('sport')) {
      if (!modifications.some(m => m.includes('body kit'))) {
        modifications.push('aggressive body kit');
      }
    }

    // If no specific modifications found, add generic sport package
    if (modifications.length === 0) {
      modifications.push('sport modification package');
    }

    return modifications;
  }

  /**
   * Generate detailed list of applied modifications with pricing
   */
  private async generateAppliedModificationsList(
    carInfo: CarInfo,
    requestedModifications: string[],
    analysisDescription: string
  ): Promise<AppliedModification[]> {
    const appliedModifications: AppliedModification[] = [];

    for (const modification of requestedModifications) {
      const modificationData = await this.createModificationData(
        carInfo,
        modification,
        analysisDescription
      );
      
      if (modificationData) {
        appliedModifications.push(modificationData);
      }
    }

    return appliedModifications;
  }

  /**
   * Create modification data with pricing
   */
  private async createModificationData(
    carInfo: CarInfo,
    modification: string,
    analysisDescription: string
  ): Promise<AppliedModification | null> {
    try {
      // Get pricing for this modification
      const pricing = await this.pricingService.getModificationPricing(
        carInfo,
        modification
      );

      const modificationData: AppliedModification = {
        type: {
          id: `mod_${modification.replace(/\s+/g, '_')}`,
          name: this.formatModificationName(modification),
          category: this.categorizeModification(modification),
          description: `${this.formatModificationName(modification)} for ${carInfo.make} ${carInfo.model}`,
          averagePrice: pricing.averagePrice,
          installationComplexity: this.getInstallationComplexity(modification),
          compatibleWith: [`${carInfo.make} ${carInfo.model}`],
        },
        specificPart: pricing.specificPart,
        brand: pricing.brand,
        partNumber: pricing.partNumber,
        price: pricing.price,
        installationTime: pricing.installationTime,
        description: `${this.formatModificationName(modification)} - ${pricing.description}`,
      };

      return modificationData;
    } catch (error) {
      logger.error(`Error creating modification data for ${modification}:`, error);
      return null;
    }
  }

  /**
   * Format modification name for display
   */
  private formatModificationName(modification: string): string {
    return modification
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  /**
   * Categorize modification type
   */
  private categorizeModification(modification: string): ModificationCategory {
    const mod = modification.toLowerCase();
    
    if (mod.includes('body kit') || mod.includes('splitter') || mod.includes('skirts')) {
      return ModificationCategory.BODY_KIT;
    }
    if (mod.includes('wheels') || mod.includes('rims')) {
      return ModificationCategory.WHEELS;
    }
    if (mod.includes('spoiler') || mod.includes('wing')) {
      return ModificationCategory.SPOILER;
    }
    if (mod.includes('suspension') || mod.includes('lower')) {
      return ModificationCategory.SUSPENSION;
    }
    if (mod.includes('exhaust')) {
      return ModificationCategory.EXHAUST;
    }
    
    return ModificationCategory.PERFORMANCE;
  }

  /**
   * Determine installation complexity
   */
  private getInstallationComplexity(modification: string): InstallationComplexity {
    const mod = modification.toLowerCase();
    
    if (mod.includes('wheels') || mod.includes('rims')) {
      return InstallationComplexity.EASY;
    }
    if (mod.includes('spoiler') || mod.includes('exhaust')) {
      return InstallationComplexity.MEDIUM;
    }
    if (mod.includes('body kit') || mod.includes('suspension')) {
      return InstallationComplexity.HARD;
    }
    
    return InstallationComplexity.MEDIUM;
  }

  /**
   * Calculate total cost breakdown
   */
  private calculateTotalCost(appliedModifications: AppliedModification[]): CostBreakdown {
    // Convert USD prices to RUB
    const exchangeRate = 95; // Approximate USD to RUB rate

    const parts = appliedModifications.reduce((sum, mod) => sum + (mod.price * exchangeRate), 0);
    const labor = appliedModifications.reduce((sum, mod) => {
      // Estimate labor cost based on complexity
      const laborMultiplier = {
        [InstallationComplexity.EASY]: 0.2,
        [InstallationComplexity.MEDIUM]: 0.5,
        [InstallationComplexity.HARD]: 1.0,
        [InstallationComplexity.PROFESSIONAL]: 1.5,
      };

      return sum + (mod.price * exchangeRate * laborMultiplier[mod.type.installationComplexity]);
    }, 0);

    const total = parts + labor;

    return {
      parts: Math.round(parts),
      labor: Math.round(labor),
      total: Math.round(total),
      currency: 'RUB',
      priceRange: {
        min: Math.round(total * 0.8),
        max: Math.round(total * 1.3),
      },
    };
  }

  /**
   * Generate installation notes
   */
  private generateInstallationNotes(appliedModifications: AppliedModification[]): string {
    const notes: string[] = [];
    
    const complexModifications = appliedModifications.filter(
      mod => mod.type.installationComplexity === InstallationComplexity.HARD ||
             mod.type.installationComplexity === InstallationComplexity.PROFESSIONAL
    );

    if (complexModifications.length > 0) {
      notes.push('⚠️ Professional installation recommended for complex modifications');
    }

    const bodyKitMods = appliedModifications.filter(
      mod => mod.type.category === ModificationCategory.BODY_KIT
    );
    
    if (bodyKitMods.length > 0) {
      notes.push('🔧 Body kit installation may require painting to match car color');
    }

    const suspensionMods = appliedModifications.filter(
      mod => mod.type.category === ModificationCategory.SUSPENSION
    );
    
    if (suspensionMods.length > 0) {
      notes.push('⚖️ Wheel alignment required after suspension modifications');
    }

    if (notes.length === 0) {
      notes.push('✅ Standard installation procedures apply');
    }

    return notes.join('\n');
  }
}
