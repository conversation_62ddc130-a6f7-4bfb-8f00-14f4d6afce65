import { CarInfo, CarRecognitionResponse } from '@/types';
import { OpenAIService } from './openai.service';
import { ValidationUtils } from '@/utils/validation';
import { logger } from '@/utils/logger';

/**
 * Enhanced car recognition service with additional validation and processing
 */
export class CarRecognitionService {
  private openAIService: OpenAIService;
  private carDatabase: Map<string, any> = new Map();

  constructor() {
    this.openAIService = new OpenAIService();
    this.initializeCarDatabase();
  }

  /**
   * Recognize car from image with enhanced validation
   */
  async recognizeCar(imageUrl: string): Promise<CarRecognitionResponse> {
    try {
      // Validate image URL
      if (!ValidationUtils.isValidImageUrl(imageUrl)) {
        throw new Error('Invalid image URL provided');
      }

      logger.info('Starting enhanced car recognition');

      // Get recognition from OpenAI
      const recognition = await this.openAIService.recognizeCar(imageUrl);

      // Validate and enhance the recognition result
      const enhancedCarInfo = await this.enhanceCarInfo(recognition.carInfo);

      // Generate additional suggestions based on car type
      const enhancedSuggestions = this.generateEnhancedSuggestions(enhancedCarInfo);

      // Calculate final confidence score
      const finalConfidence = this.calculateFinalConfidence(
        recognition.confidence,
        enhancedCarInfo
      );

      const result: CarRecognitionResponse = {
        carInfo: {
          ...enhancedCarInfo,
          confidence: finalConfidence,
        },
        suggestions: enhancedSuggestions,
        confidence: finalConfidence,
      };

      logger.info(`Enhanced car recognition completed: ${result.carInfo.make} ${result.carInfo.model}`);
      return result;

    } catch (error) {
      logger.error('Error in car recognition service:', error);
      throw new Error('Failed to recognize car from image');
    }
  }

  /**
   * Enhance car information with database lookup and validation
   */
  private async enhanceCarInfo(carInfo: CarInfo): Promise<CarInfo> {
    try {
      // Validate basic car info
      const validatedInfo = ValidationUtils.validateCarInfo(carInfo);

      // Normalize make and model names
      const normalizedMake = this.normalizeMakeName(validatedInfo.make);
      const normalizedModel = this.normalizeModelName(validatedInfo.model);

      // Look up additional information from car database
      const dbInfo = this.lookupCarInDatabase(normalizedMake, normalizedModel, validatedInfo.year);

      // Enhance with database information
      const enhanced: CarInfo = {
        ...validatedInfo,
        make: normalizedMake,
        model: normalizedModel,
        generation: dbInfo?.generation || validatedInfo.generation,
        bodyType: dbInfo?.bodyType || validatedInfo.bodyType,
        detectedFeatures: [
          ...validatedInfo.detectedFeatures,
          ...(dbInfo?.commonFeatures || [])
        ].filter((feature, index, array) => array.indexOf(feature) === index), // Remove duplicates
      };

      return enhanced;
    } catch (error) {
      logger.error('Error enhancing car info:', error);
      return carInfo; // Return original if enhancement fails
    }
  }

  /**
   * Generate enhanced modification suggestions based on car type
   */
  private generateEnhancedSuggestions(carInfo: CarInfo): string[] {
    const suggestions: string[] = [];
    const make = carInfo.make.toLowerCase();
    const bodyType = carInfo.bodyType.toLowerCase();
    const year = carInfo.year;

    // Base suggestions for all cars
    suggestions.push('Sport wheels and tires');
    suggestions.push('Window tinting');

    // Body type specific suggestions
    if (bodyType.includes('sedan')) {
      suggestions.push('Rear spoiler');
      suggestions.push('Side skirts');
      suggestions.push('Lowering springs');
    } else if (bodyType.includes('hatchback')) {
      suggestions.push('Roof spoiler');
      suggestions.push('Front splitter');
      suggestions.push('Sport exhaust');
    } else if (bodyType.includes('suv') || bodyType.includes('crossover')) {
      suggestions.push('Running boards');
      suggestions.push('Roof rails');
      suggestions.push('Aggressive grille');
    } else if (bodyType.includes('coupe')) {
      suggestions.push('Body kit package');
      suggestions.push('Performance exhaust');
      suggestions.push('Carbon fiber accents');
    }

    // Make-specific suggestions
    if (make.includes('bmw')) {
      suggestions.push('M-Sport body kit');
      suggestions.push('Kidney grille upgrade');
    } else if (make.includes('audi')) {
      suggestions.push('S-Line body kit');
      suggestions.push('RS-style grille');
    } else if (make.includes('mercedes')) {
      suggestions.push('AMG body kit');
      suggestions.push('AMG wheels');
    } else if (make.includes('volkswagen') || make.includes('vw')) {
      suggestions.push('GTI-style body kit');
      suggestions.push('R-Line package');
    } else if (make.includes('honda')) {
      suggestions.push('Type R inspired modifications');
      suggestions.push('Mugen body kit');
    } else if (make.includes('toyota')) {
      suggestions.push('TRD body kit');
      suggestions.push('Sport package');
    }

    // Year-based suggestions
    if (year >= 2020) {
      suggestions.push('LED light upgrades');
      suggestions.push('Digital dashboard enhancement');
    } else if (year >= 2010) {
      suggestions.push('Xenon headlight conversion');
      suggestions.push('Modern wheel designs');
    }

    // Remove duplicates and limit to reasonable number
    return [...new Set(suggestions)].slice(0, 8);
  }

  /**
   * Calculate final confidence score based on multiple factors
   */
  private calculateFinalConfidence(
    baseConfidence: number,
    carInfo: CarInfo
  ): number {
    let confidence = baseConfidence;

    // Boost confidence if car is in our database
    const dbEntry = this.lookupCarInDatabase(carInfo.make, carInfo.model, carInfo.year);
    if (dbEntry) {
      confidence = Math.min(confidence + 0.1, 1.0);
    }

    // Reduce confidence for very old or very new cars
    const currentYear = new Date().getFullYear();
    if (carInfo.year < 2000 || carInfo.year > currentYear) {
      confidence = Math.max(confidence - 0.1, 0.0);
    }

    // Boost confidence if we have many detected features
    if (carInfo.detectedFeatures.length >= 5) {
      confidence = Math.min(confidence + 0.05, 1.0);
    }

    return Math.round(confidence * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Normalize car make names to standard format
   */
  private normalizeMakeName(make: string): string {
    const makeMap: Record<string, string> = {
      'vw': 'Volkswagen',
      'bmw': 'BMW',
      'mb': 'Mercedes-Benz',
      'mercedes': 'Mercedes-Benz',
      'benz': 'Mercedes-Benz',
      'audi': 'Audi',
      'toyota': 'Toyota',
      'honda': 'Honda',
      'nissan': 'Nissan',
      'ford': 'Ford',
      'chevrolet': 'Chevrolet',
      'chevy': 'Chevrolet',
      'hyundai': 'Hyundai',
      'kia': 'Kia',
      'mazda': 'Mazda',
      'subaru': 'Subaru',
      'lexus': 'Lexus',
      'infiniti': 'Infiniti',
      'acura': 'Acura',
      'volvo': 'Volvo',
      'jaguar': 'Jaguar',
      'land rover': 'Land Rover',
      'porsche': 'Porsche',
      'ferrari': 'Ferrari',
      'lamborghini': 'Lamborghini',
      'maserati': 'Maserati',
      'bentley': 'Bentley',
      'rolls royce': 'Rolls-Royce',
    };

    const normalized = makeMap[make.toLowerCase()] || make;
    return normalized.charAt(0).toUpperCase() + normalized.slice(1);
  }

  /**
   * Normalize car model names
   */
  private normalizeModelName(model: string): string {
    // Remove common suffixes and normalize
    return model
      .replace(/\s+(sedan|hatchback|coupe|wagon|suv)$/i, '')
      .trim()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  /**
   * Look up car information in database
   */
  private lookupCarInDatabase(make: string, model: string, year: number): any {
    const key = `${make.toLowerCase()}_${model.toLowerCase()}_${year}`;
    return this.carDatabase.get(key);
  }

  /**
   * Initialize car database with common models and their characteristics
   */
  private initializeCarDatabase(): void {
    // This would be populated from a real database in production
    const commonCars = [
      {
        make: 'BMW',
        model: '3 Series',
        years: [2019, 2020, 2021, 2022, 2023],
        bodyType: 'sedan',
        generation: 'G20',
        commonFeatures: ['kidney grille', 'angel eyes', 'hofmeister kink'],
      },
      {
        make: 'Audi',
        model: 'A4',
        years: [2017, 2018, 2019, 2020, 2021, 2022, 2023],
        bodyType: 'sedan',
        generation: 'B9',
        commonFeatures: ['quattro badge', 'singleframe grille', 'LED DRL'],
      },
      {
        make: 'Mercedes-Benz',
        model: 'C-Class',
        years: [2019, 2020, 2021, 2022, 2023],
        bodyType: 'sedan',
        generation: 'W205',
        commonFeatures: ['three-pointed star', 'LED headlights', 'chrome accents'],
      },
      {
        make: 'Volkswagen',
        model: 'Golf',
        years: [2020, 2021, 2022, 2023],
        bodyType: 'hatchback',
        generation: 'Mk8',
        commonFeatures: ['VW badge', 'C-pillar design', 'compact proportions'],
      },
      {
        make: 'Toyota',
        model: 'Camry',
        years: [2018, 2019, 2020, 2021, 2022, 2023],
        bodyType: 'sedan',
        generation: 'XV70',
        commonFeatures: ['aggressive front end', 'Toyota badge', 'LED headlights'],
      },
    ];

    // Populate database
    commonCars.forEach(car => {
      car.years.forEach(year => {
        const key = `${car.make.toLowerCase()}_${car.model.toLowerCase()}_${year}`;
        this.carDatabase.set(key, {
          generation: car.generation,
          bodyType: car.bodyType,
          commonFeatures: car.commonFeatures,
        });
      });
    });

    logger.info(`Car database initialized with ${this.carDatabase.size} entries`);
  }

  /**
   * Get car specifications for modification compatibility
   */
  async getCarSpecifications(carInfo: CarInfo): Promise<any> {
    try {
      const specs = this.lookupCarInDatabase(carInfo.make, carInfo.model, carInfo.year);
      
      if (!specs) {
        // Return default specifications
        return {
          wheelBoltPattern: 'Unknown',
          suspensionType: 'Unknown',
          engineType: 'Unknown',
          transmissionType: 'Unknown',
          driveType: 'Unknown',
        };
      }

      return specs;
    } catch (error) {
      logger.error('Error getting car specifications:', error);
      return null;
    }
  }
}
