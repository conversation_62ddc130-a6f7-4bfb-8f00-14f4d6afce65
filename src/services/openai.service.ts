import { CarInfo, CarRecognitionResponse, OpenAIImageResponse } from '../types/index';
import { logger } from '../utils/logger';
import { ProxyAPIService } from './proxyapi.service';
import { OpenRouterService } from './openrouter.service';

export class OpenAIService {
  private proxyAPIService: ProxyAPIService;
  private openRouterService: OpenRouterService;

  constructor() {
    this.proxyAPIService = new ProxyAPIService();
    this.openRouterService = new OpenRouterService();
  }

  /**
   * Recognize car make, model, and year from image using OpenRouter GPT-4o Vision
   */
  async recognizeCar(imageUrl: string): Promise<CarRecognitionResponse> {
    return this.openRouterService.recognizeCar(imageUrl);
  }

  /**
   * Generate modification prompt for DALL-E based on car info and requested modifications
   */
  private generateModificationPrompt(
    carInfo: CarInfo,
    modifications: string[],
    userRequest: string
  ): string {
    const carDescription = `${carInfo.year} ${carInfo.make} ${carInfo.model}`;
    const bodyType = carInfo.bodyType.toLowerCase();

    // Create a detailed prompt for realistic car modification
    return `Professional automotive photography of a modified ${carDescription} ${bodyType}.

    Base vehicle: ${carDescription} in ${bodyType} configuration
    Applied modifications: ${modifications.join(', ')}
    User specification: "${userRequest}"

    Visual requirements:
    - Ultra-realistic automotive photography quality
    - Professional studio lighting or natural outdoor lighting
    - Clean background (white studio or scenic outdoor setting)
    - 3/4 front view angle showing modifications clearly
    - High-resolution detail showing all modification elements
    - Commercially available aftermarket parts only
    - Professional installation appearance
    - Maintain original vehicle proportions and stance

    Modification details:
    - All parts should look like real, purchasable aftermarket components
    - Proper fitment and finish matching the vehicle
    - Realistic material textures (carbon fiber, painted plastic, metal)
    - Appropriate sizing for the specific car model
    - Professional paint matching where applicable

    Photography style: Automotive magazine quality, sharp focus, proper exposure, vibrant but realistic colors.`;
  }

  /**
   * Generate modified car image using ProxyAPI gpt-image-1
   */
  async generateModifiedImage(
    originalImageUrl: string,
    carInfo: CarInfo,
    modifications: string[],
    userRequest: string
  ): Promise<OpenAIImageResponse> {
    return this.proxyAPIService.generateModifiedImage(
      originalImageUrl,
      carInfo,
      modifications,
      userRequest
    );
  }

  /**
   * Analyze applied modifications and generate detailed description using OpenRouter
   */
  async analyzeModifications(
    originalImageUrl: string,
    modifiedImageUrl: string,
    carInfo: CarInfo
  ): Promise<string> {
    return this.openRouterService.analyzeModifications(
      originalImageUrl,
      modifiedImageUrl,
      carInfo
    );
  }
}
