import { UserSession, ConversationStep, CarInfo } from '@/types';
import { logger } from '@/utils/logger';

/**
 * In-memory session management service
 * In production, this should be replaced with Redis or database storage
 */
export class SessionManager {
  private sessions: Map<number, UserSession> = new Map();
  private readonly SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes

  constructor() {
    // Clean up expired sessions every 5 minutes
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, 5 * 60 * 1000);
  }

  /**
   * Create a new user session
   */
  async createSession(userId: number, chatId: number): Promise<UserSession> {
    const session: UserSession = {
      userId,
      chatId,
      currentStep: ConversationStep.WAITING_FOR_IMAGE,
      lastActivity: new Date(),
    };

    this.sessions.set(userId, session);
    logger.info(`Created session for user ${userId}`);
    
    return session;
  }

  /**
   * Get existing user session
   */
  async getSession(userId: number): Promise<UserSession | null> {
    const session = this.sessions.get(userId);
    
    if (!session) {
      return null;
    }

    // Check if session is expired
    const now = new Date();
    const timeDiff = now.getTime() - session.lastActivity.getTime();
    
    if (timeDiff > this.SESSION_TIMEOUT) {
      this.sessions.delete(userId);
      logger.info(`Session expired for user ${userId}`);
      return null;
    }

    // Update last activity
    session.lastActivity = now;
    return session;
  }

  /**
   * Update session data
   */
  async updateSession(
    userId: number, 
    updates: Partial<Omit<UserSession, 'userId' | 'chatId'>>
  ): Promise<UserSession | null> {
    const session = await this.getSession(userId);
    
    if (!session) {
      logger.warn(`Attempted to update non-existent session for user ${userId}`);
      return null;
    }

    // Apply updates
    Object.assign(session, updates, { lastActivity: new Date() });
    
    this.sessions.set(userId, session);
    logger.debug(`Updated session for user ${userId}`, updates);
    
    return session;
  }

  /**
   * Delete user session
   */
  async deleteSession(userId: number): Promise<boolean> {
    const deleted = this.sessions.delete(userId);
    
    if (deleted) {
      logger.info(`Deleted session for user ${userId}`);
    }
    
    return deleted;
  }

  /**
   * Get all active sessions (for admin purposes)
   */
  async getActiveSessions(): Promise<UserSession[]> {
    return Array.from(this.sessions.values());
  }

  /**
   * Clean up expired sessions
   */
  private cleanupExpiredSessions(): void {
    const now = new Date();
    let cleanedCount = 0;

    for (const [userId, session] of this.sessions.entries()) {
      const timeDiff = now.getTime() - session.lastActivity.getTime();
      
      if (timeDiff > this.SESSION_TIMEOUT) {
        this.sessions.delete(userId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.info(`Cleaned up ${cleanedCount} expired sessions`);
    }
  }

  /**
   * Get session statistics
   */
  async getSessionStats(): Promise<{
    total: number;
    byStep: Record<ConversationStep, number>;
  }> {
    const sessions = Array.from(this.sessions.values());
    const byStep: Record<ConversationStep, number> = {
      [ConversationStep.WAITING_FOR_IMAGE]: 0,
      [ConversationStep.PROCESSING_IMAGE]: 0,
      [ConversationStep.CAR_RECOGNIZED]: 0,
      [ConversationStep.WAITING_FOR_MODIFICATIONS]: 0,
      [ConversationStep.GENERATING_MODIFICATIONS]: 0,
      [ConversationStep.SHOWING_RESULTS]: 0,
    };

    sessions.forEach(session => {
      byStep[session.currentStep]++;
    });

    return {
      total: sessions.length,
      byStep,
    };
  }
}
