import TelegramBot from 'node-telegram-bot-api';
import { telegramConfig } from '@/config';
import { UserSession, ConversationStep, TelegramUser } from '@/types';
import { logger } from '@/utils/logger';
import { SessionManager } from './session.service';
import { ModificationService } from './modification.service';
import { CostFormatterService } from './cost-formatter.service';
import axios from 'axios';

export class TelegramService {
  private bot: TelegramBot;
  private sessionManager: SessionManager;
  private modificationService: ModificationService;
  private costFormatter: CostFormatterService;

  constructor() {
    this.bot = new TelegramBot(telegramConfig.token, telegramConfig.options);
    this.sessionManager = new SessionManager();
    this.modificationService = new ModificationService();
    this.costFormatter = new CostFormatterService();
    this.setupHandlers();
  }

  private setupHandlers(): void {
    // Start command
    this.bot.onText(/\/start/, (msg) => {
      this.handleStart(msg);
    });

    // Help command
    this.bot.onText(/\/help/, (msg) => {
      this.handleHelp(msg);
    });

    // Photo upload handler
    this.bot.on('photo', (msg) => {
      this.handlePhoto(msg);
    });

    // Text message handler
    this.bot.on('message', (msg) => {
      if (msg.text && !msg.text.startsWith('/') && !msg.photo) {
        this.handleTextMessage(msg);
      }
    });

    // Error handler
    this.bot.on('polling_error', (error) => {
      logger.error('Telegram polling error:', error);
    });

    logger.info('Telegram bot handlers set up successfully');
  }

  private async handleStart(msg: TelegramBot.Message): Promise<void> {
    const chatId = msg.chat.id;
    const user: TelegramUser = {
      id: msg.from?.id || 0,
      username: msg.from?.username,
      firstName: msg.from?.first_name || 'User',
      lastName: msg.from?.last_name,
      languageCode: msg.from?.language_code,
    };

    // Initialize user session
    await this.sessionManager.createSession(user.id, chatId);

    const welcomeMessage = `
🚗 Welcome to Car Modification Generator! 

I can help you visualize modifications for your car using AI. Here's how it works:

1️⃣ Send me a photo of your car
2️⃣ Tell me what modifications you want (body kit, wheels, spoiler, etc.)
3️⃣ Get a realistic visualization with cost estimates

📸 Send me a car photo to get started!

Use /help for more information.
    `;

    await this.sendMessage(chatId, welcomeMessage);
  }

  private async handleHelp(msg: TelegramBot.Message): Promise<void> {
    const chatId = msg.chat.id;
    
    const helpMessage = `
🔧 Car Modification Generator Help

📸 **How to use:**
1. Send a clear photo of your car (side view works best)
2. Wait for car recognition
3. Describe the modifications you want
4. Get your visualization with cost breakdown

🎯 **Supported modifications:**
• Body kits and aerodynamic packages
• Wheels and rims
• Spoilers and wings
• Lowering and suspension
• Exhaust systems
• Lighting modifications

💡 **Tips for best results:**
• Use high-quality, well-lit photos
• Side or 3/4 view angles work best
• Avoid heavily modified cars as base
• Be specific about what you want

📋 **Commands:**
/start - Start over
/help - Show this help
    `;

    await this.sendMessage(chatId, helpMessage);
  }

  private async handlePhoto(msg: TelegramBot.Message): Promise<void> {
    const chatId = msg.chat.id;
    const userId = msg.from?.id || 0;

    try {
      const session = await this.sessionManager.getSession(userId);
      
      if (!session || session.currentStep !== ConversationStep.WAITING_FOR_IMAGE) {
        await this.sendMessage(chatId, 
          "Please start with /start command first, then send your car photo.");
        return;
      }

      // Get the highest resolution photo
      const photo = msg.photo?.[msg.photo.length - 1];
      if (!photo) {
        await this.sendMessage(chatId, "❌ No photo received. Please try again.");
        return;
      }

      await this.sendMessage(chatId, "📸 Photo received! Analyzing your car...");

      // Update session
      await this.sessionManager.updateSession(userId, {
        currentStep: ConversationStep.PROCESSING_IMAGE,
        uploadedImage: photo.file_id,
      });

      // Get file URL
      const fileUrl = await this.bot.getFileLink(photo.file_id);
      
      // Process the image
      const result = await this.modificationService.recognizeCar(fileUrl);

      if (result.carInfo.confidence < 0.6) {
        await this.sendMessage(chatId, 
          `⚠️ I'm not very confident about the car recognition (${Math.round(result.carInfo.confidence * 100)}% confidence). 
          Please try with a clearer photo or a different angle.`);
        
        await this.sessionManager.updateSession(userId, {
          currentStep: ConversationStep.WAITING_FOR_IMAGE,
        });
        return;
      }

      // Car recognized successfully
      const carInfo = result.carInfo;
      await this.sessionManager.updateSession(userId, {
        currentStep: ConversationStep.CAR_RECOGNIZED,
        carInfo: carInfo,
      });

      const recognitionMessage = `
✅ **Car Recognized!**

🚗 **Vehicle:** ${carInfo.year} ${carInfo.make} ${carInfo.model}
📊 **Confidence:** ${Math.round(carInfo.confidence * 100)}%
🏗️ **Body Type:** ${carInfo.bodyType}

💡 **Popular modifications for this car:**
${result.suggestions.map(s => `• ${s}`).join('\n')}

Now tell me what modifications you'd like to see! You can say something like:
"Add a sport body kit and black wheels"
"I want a rear spoiler and lowered suspension"
"Make it look more aggressive with a front splitter"
      `;

      await this.sendMessage(chatId, recognitionMessage);

    } catch (error) {
      logger.error('Error handling photo:', error);
      await this.sendMessage(chatId, 
        "❌ Sorry, I couldn't process your photo. Please try again with a different image.");
      
      await this.sessionManager.updateSession(userId, {
        currentStep: ConversationStep.WAITING_FOR_IMAGE,
      });
    }
  }

  private async handleTextMessage(msg: TelegramBot.Message): Promise<void> {
    const chatId = msg.chat.id;
    const userId = msg.from?.id || 0;
    const text = msg.text || '';

    try {
      const session = await this.sessionManager.getSession(userId);
      
      if (!session) {
        await this.sendMessage(chatId, "Please start with /start command first.");
        return;
      }

      if (session.currentStep === ConversationStep.WAITING_FOR_IMAGE) {
        await this.sendMessage(chatId, "📸 Please send a photo of your car first.");
        return;
      }

      if (session.currentStep === ConversationStep.CAR_RECOGNIZED) {
        await this.handleModificationRequest(chatId, userId, text, session);
      } else {
        await this.sendMessage(chatId, "I'm not sure what you want to do. Use /start to begin.");
      }

    } catch (error) {
      logger.error('Error handling text message:', error);
      await this.sendMessage(chatId, "❌ Something went wrong. Please try again.");
    }
  }

  private async handleModificationRequest(
    chatId: number, 
    userId: number, 
    request: string, 
    session: UserSession
  ): Promise<void> {
    if (!session.carInfo || !session.uploadedImage) {
      await this.sendMessage(chatId, "❌ Session error. Please start over with /start");
      return;
    }

    await this.sendMessage(chatId, "🎨 Generating your car modifications... This may take a minute!");

    await this.sessionManager.updateSession(userId, {
      currentStep: ConversationStep.GENERATING_MODIFICATIONS,
    });

    try {
      // Get original image URL
      const originalImageUrl = await this.bot.getFileLink(session.uploadedImage);
      
      // Generate modifications
      const result = await this.modificationService.generateModifications(
        originalImageUrl,
        session.carInfo,
        request
      );

      // Update session
      await this.sessionManager.updateSession(userId, {
        currentStep: ConversationStep.SHOWING_RESULTS,
      });

      // Send results
      await this.sendModificationResults(chatId, result, originalImageUrl);

    } catch (error) {
      logger.error('Error generating modifications:', error);
      await this.sendMessage(chatId, 
        "❌ Sorry, I couldn't generate the modifications. Please try a different request.");
      
      await this.sessionManager.updateSession(userId, {
        currentStep: ConversationStep.CAR_RECOGNIZED,
      });
    }
  }

  private async sendModificationResults(
    chatId: number,
    result: any,
    originalImageUrl: string
  ): Promise<void> {
    try {
      // Всегда пытаемся отправить модифицированное изображение
      if (result.modifiedImageUrl) {
        logger.info(`Sending modified image: ${result.modifiedImageUrl}`);

        try {
          // Пытаемся отправить изображение как URL
          await this.bot.sendPhoto(chatId, result.modifiedImageUrl, {
            caption: `🎨 **Ваш автомобиль с модификациями**

✨ Модификации применены успешно!

💡 *Так будет выглядеть ваш автомобиль*`
          });
        } catch (urlError) {
          logger.warn('Failed to send image by URL, trying to download and send as file:', urlError);

          // Если не получилось отправить по URL, загружаем и отправляем как файл
          try {
            const imageBuffer = await this.downloadImageAsBuffer(result.modifiedImageUrl);
            await this.bot.sendPhoto(chatId, imageBuffer, {
              caption: `🎨 **Ваш автомобиль с модификациями**

✨ Модификации применены успешно!

💡 *Так будет выглядеть ваш автомобиль*`
            });
          } catch (bufferError) {
            logger.error('Failed to send image as buffer:', bufferError);
            throw bufferError;
          }
        }

        // Отправляем детальную стоимость модификаций
        if (result.appliedModifications && result.appliedModifications.length > 0) {
          const costBreakdown = this.costFormatter.formatCostBreakdown(
            result.appliedModifications,
            result.totalCost
          );
          await this.sendMessage(chatId, costBreakdown);
        }

        // Отправляем детальное описание отдельным сообщением
        if (result.description && result.description.length > 50) {
          // Обрезаем описание если оно слишком длинное
          const shortDescription = result.description.length > 500
            ? result.description.substring(0, 500) + '...'
            : result.description;

          await this.sendMessage(chatId, `📝 **Детали модификаций:**\n\n${shortDescription}`);
        }

        // Отправляем краткое сообщение с вопросом о других модификациях
        await this.sendMessage(chatId, `Хотите попробовать другие модификации? Просто опишите, что хотели бы изменить! 🚗✨`);

      } else {
        // Если изображение не было сгенерировано, но есть информация о модификациях
        await this.sendMessage(chatId, `⚠️ **Не удалось создать визуализацию модификаций**

📝 **Запрошенные изменения:**
${result.description}

Попробуйте переформулировать запрос или попробовать позже.`);

        // Отправляем стоимость даже если изображение не создалось
        if (result.appliedModifications && result.appliedModifications.length > 0) {
          const costBreakdown = this.costFormatter.formatCostBreakdown(
            result.appliedModifications,
            result.totalCost
          );
          await this.sendMessage(chatId, costBreakdown);
        }
      }
    } catch (imageError) {
      logger.error('Failed to send modified image:', imageError);

      // Fallback - отправляем только текст
      await this.sendMessage(chatId, `⚠️ **Ошибка при отправке изображения**

📝 **Запрошенные модификации:**
${result.description}

Попробуйте ещё раз или переформулируйте запрос.`);

      // Отправляем стоимость даже при ошибке изображения
      if (result.appliedModifications && result.appliedModifications.length > 0) {
        try {
          const costBreakdown = this.costFormatter.formatCostBreakdown(
            result.appliedModifications,
            result.totalCost
          );
          await this.sendMessage(chatId, costBreakdown);
        } catch (costError) {
          logger.error('Failed to send cost breakdown:', costError);
        }
      }
    }
  }

  /**
   * Загрузить изображение как Buffer для отправки в Telegram
   */
  private async downloadImageAsBuffer(imageUrl: string): Promise<Buffer> {
    try {
      logger.info(`Downloading image from: ${imageUrl}`);

      const response = await axios.get(imageUrl, {
        responseType: 'arraybuffer',
        timeout: 15000, // 15 секунд таймаут
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      const buffer = Buffer.from(response.data);
      logger.info(`Image downloaded successfully, size: ${buffer.length} bytes`);

      return buffer;
    } catch (error) {
      logger.error('Failed to download image:', error);
      throw new Error(`Failed to download image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async sendMessage(chatId: number, text: string): Promise<void> {
    try {
      await this.bot.sendMessage(chatId, text, { parse_mode: 'Markdown' });
    } catch (error) {
      logger.error('Error sending message:', error);
      // Fallback without markdown
      await this.bot.sendMessage(chatId, text);
    }
  }

  public start(): void {
    logger.info('Telegram bot started successfully');
  }

  public stop(): void {
    this.bot.stopPolling();
    logger.info('Telegram bot stopped');
  }
}
