#!/usr/bin/env python3
"""
vLLM Server Implementation for NVIDIA GTX 1080 Ti

This server provides an optimized vLLM implementation specifically configured
for the GTX 1080 Ti's 11GB VRAM limitation with Pascal architecture.

Features:
- Memory-optimized model loading
- Dynamic batch sizing
- GPU memory monitoring
- RESTful API interface
- Streaming support
- Model hot-swapping capabilities

Usage:
    python vllm_server.py --model mistralai/Mistral-7B-Instruct-v0.1 --port 8000
"""

import os
import sys
import argparse
import asyncio
import json
import time
import psutil
import logging
from typing import Dict, List, Optional, AsyncGenerator, Union
from pathlib import Path
from contextlib import asynccontextmanager

import torch
import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field

try:
    from vllm import LLM, SamplingParams
    from vllm.engine.arg_utils import AsyncEngineArgs
    from vllm.engine.async_llm_engine import AsyncLLMEngine
    from vllm.utils import random_uuid
except ImportError as e:
    print(f"Error importing vLLM: {e}")
    print("Please install vLLM: pip install vllm")
    sys.exit(1)


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class GTX1080TiConfig:
    """Configuration optimized for GTX 1080 Ti (11GB VRAM, Pascal architecture)"""
    
    # Hardware specifications
    GPU_MEMORY_GB = 11
    COMPUTE_CAPABILITY = 6.1  # Pascal architecture
    CUDA_CORES = 3584
    
    # Memory management
    GPU_MEMORY_UTILIZATION = 0.85  # Conservative for stability
    MAX_MODEL_LEN = 4096  # Reasonable context length
    SWAP_SPACE_GB = 4  # CPU swap space
    
    # Performance settings
    MAX_NUM_SEQS = 16  # Concurrent sequences for 7B models
    MAX_NUM_BATCHED_TOKENS = 2048
    TENSOR_PARALLEL_SIZE = 1  # Single GPU
    
    # Model configurations for different sizes
    MODEL_CONFIGS = {
        "3b": {
            "gpu_memory_utilization": 0.9,
            "max_model_len": 8192,
            "max_num_seqs": 32,
            "max_num_batched_tokens": 4096,
        },
        "7b": {
            "gpu_memory_utilization": 0.85,
            "max_model_len": 4096,
            "max_num_seqs": 16,
            "max_num_batched_tokens": 2048,
        },
        "13b_quantized": {
            "gpu_memory_utilization": 0.95,
            "max_model_len": 2048,
            "max_num_seqs": 8,
            "max_num_batched_tokens": 1024,
        }
    }


class GenerationRequest(BaseModel):
    prompt: str
    max_tokens: int = Field(default=512, le=2048)
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    top_p: float = Field(default=0.9, ge=0.0, le=1.0)
    top_k: int = Field(default=-1, ge=-1)
    frequency_penalty: float = Field(default=0.0, ge=-2.0, le=2.0)
    presence_penalty: float = Field(default=0.0, ge=-2.0, le=2.0)
    stop: Optional[List[str]] = None
    stream: bool = False
    seed: Optional[int] = None


class GenerationResponse(BaseModel):
    text: str
    finish_reason: str
    usage: Dict[str, int]
    model: str
    created: int


class VLLMServer:
    def __init__(self, model_name: str, model_size: str = "7b"):
        self.model_name = model_name
        self.model_size = model_size
        self.config = GTX1080TiConfig()
        self.engine: Optional[AsyncLLMEngine] = None
        self.model_config = self.config.MODEL_CONFIGS.get(model_size, self.config.MODEL_CONFIGS["7b"])
        
    async def initialize_engine(self):
        """Initialize the vLLM engine with GTX 1080 Ti optimizations."""
        logger.info(f"Initializing vLLM engine for model: {self.model_name}")
        logger.info(f"Model size configuration: {self.model_size}")
        
        # Check GPU memory before loading
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            logger.info(f"Available GPU memory: {gpu_memory:.1f} GB")
            
            if gpu_memory < 10:
                logger.warning("GPU has less than 10GB memory. Consider using a smaller model or quantization.")
        
        # Configure engine arguments
        engine_args = AsyncEngineArgs(
            model=self.model_name,
            tensor_parallel_size=self.config.TENSOR_PARALLEL_SIZE,
            gpu_memory_utilization=self.model_config["gpu_memory_utilization"],
            max_model_len=self.model_config["max_model_len"],
            max_num_seqs=self.model_config["max_num_seqs"],
            max_num_batched_tokens=self.model_config["max_num_batched_tokens"],
            dtype="float16",  # Use FP16 for memory efficiency
            seed=42,
            trust_remote_code=True,
            disable_log_stats=False,
            swap_space=self.config.SWAP_SPACE_GB,
        )
        
        # Add quantization if specified in model name
        if "awq" in self.model_name.lower():
            engine_args.quantization = "awq"
        elif "gptq" in self.model_name.lower():
            engine_args.quantization = "gptq"
        
        try:
            self.engine = AsyncLLMEngine.from_engine_args(engine_args)
            logger.info("✅ vLLM engine initialized successfully")
            
            # Log memory usage
            if torch.cuda.is_available():
                memory_allocated = torch.cuda.memory_allocated(0) / (1024**3)
                memory_reserved = torch.cuda.memory_reserved(0) / (1024**3)
                logger.info(f"GPU memory allocated: {memory_allocated:.2f} GB")
                logger.info(f"GPU memory reserved: {memory_reserved:.2f} GB")
                
        except Exception as e:
            logger.error(f"Failed to initialize vLLM engine: {e}")
            raise
    
    async def generate(self, request: GenerationRequest) -> Union[GenerationResponse, AsyncGenerator]:
        """Generate text using the vLLM engine."""
        if not self.engine:
            raise HTTPException(status_code=503, detail="Model not loaded")
        
        # Create sampling parameters
        sampling_params = SamplingParams(
            temperature=request.temperature,
            top_p=request.top_p,
            top_k=request.top_k if request.top_k > 0 else None,
            max_tokens=request.max_tokens,
            frequency_penalty=request.frequency_penalty,
            presence_penalty=request.presence_penalty,
            stop=request.stop,
            seed=request.seed,
        )
        
        request_id = random_uuid()
        
        try:
            if request.stream:
                return self._stream_generate(request.prompt, sampling_params, request_id)
            else:
                return await self._generate_single(request.prompt, sampling_params, request_id)
                
        except Exception as e:
            logger.error(f"Generation error: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def _generate_single(self, prompt: str, sampling_params: SamplingParams, request_id: str) -> GenerationResponse:
        """Generate a single response."""
        start_time = time.time()
        
        # Add the request to the engine
        results_generator = self.engine.generate(prompt, sampling_params, request_id)
        
        # Get the final result
        final_output = None
        async for request_output in results_generator:
            final_output = request_output
        
        if not final_output:
            raise HTTPException(status_code=500, detail="No output generated")
        
        output = final_output.outputs[0]
        
        return GenerationResponse(
            text=output.text,
            finish_reason=output.finish_reason,
            usage={
                "prompt_tokens": len(final_output.prompt_token_ids),
                "completion_tokens": len(output.token_ids),
                "total_tokens": len(final_output.prompt_token_ids) + len(output.token_ids),
            },
            model=self.model_name,
            created=int(start_time)
        )
    
    async def _stream_generate(self, prompt: str, sampling_params: SamplingParams, request_id: str) -> AsyncGenerator:
        """Generate streaming response."""
        results_generator = self.engine.generate(prompt, sampling_params, request_id)
        
        async for request_output in results_generator:
            output = request_output.outputs[0]
            
            chunk = {
                "text": output.text,
                "finish_reason": output.finish_reason,
                "model": self.model_name,
            }
            
            yield f"data: {json.dumps(chunk)}\n\n"
        
        yield "data: [DONE]\n\n"
    
    def get_stats(self) -> Dict:
        """Get server and GPU statistics."""
        stats = {
            "model": self.model_name,
            "model_size": self.model_size,
            "gpu_available": torch.cuda.is_available(),
        }
        
        if torch.cuda.is_available():
            stats.update({
                "gpu_name": torch.cuda.get_device_name(0),
                "gpu_memory_total": torch.cuda.get_device_properties(0).total_memory / (1024**3),
                "gpu_memory_allocated": torch.cuda.memory_allocated(0) / (1024**3),
                "gpu_memory_reserved": torch.cuda.memory_reserved(0) / (1024**3),
            })
        
        # System stats
        stats.update({
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_usage": psutil.disk_usage('/').percent,
        })
        
        return stats


# Global server instance
server_instance: Optional[VLLMServer] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage the lifecycle of the vLLM server."""
    global server_instance
    
    # Startup
    logger.info("Starting vLLM server...")
    if server_instance:
        await server_instance.initialize_engine()
    
    yield
    
    # Shutdown
    logger.info("Shutting down vLLM server...")


# Create FastAPI app
app = FastAPI(
    title="vLLM Server for GTX 1080 Ti",
    description="Optimized vLLM server for NVIDIA GTX 1080 Ti",
    version="1.0.0",
    lifespan=lifespan
)


@app.post("/v1/completions", response_model=GenerationResponse)
async def create_completion(request: GenerationRequest):
    """Create a text completion."""
    if not server_instance:
        raise HTTPException(status_code=503, detail="Server not initialized")
    
    if request.stream:
        return StreamingResponse(
            await server_instance.generate(request),
            media_type="text/plain"
        )
    else:
        return await server_instance.generate(request)


@app.get("/v1/models")
async def list_models():
    """List available models."""
    if not server_instance:
        raise HTTPException(status_code=503, detail="Server not initialized")
    
    return {
        "object": "list",
        "data": [
            {
                "id": server_instance.model_name,
                "object": "model",
                "created": int(time.time()),
                "owned_by": "vllm",
            }
        ]
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    if not server_instance or not server_instance.engine:
        raise HTTPException(status_code=503, detail="Server not ready")
    
    return {"status": "healthy", "model": server_instance.model_name}


@app.get("/stats")
async def get_stats():
    """Get server statistics."""
    if not server_instance:
        raise HTTPException(status_code=503, detail="Server not initialized")
    
    return server_instance.get_stats()


def main():
    parser = argparse.ArgumentParser(description="vLLM Server for GTX 1080 Ti")
    parser.add_argument("--model", required=True, help="Model name or path")
    parser.add_argument("--model-size", default="7b", choices=["3b", "7b", "13b_quantized"], 
                       help="Model size category for optimization")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--log-level", default="info", help="Log level")
    
    args = parser.parse_args()
    
    # Set environment variables for optimal performance
    os.environ["CUDA_VISIBLE_DEVICES"] = "0"
    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:128"
    
    # Initialize global server instance
    global server_instance
    server_instance = VLLMServer(args.model, args.model_size)
    
    logger.info(f"Starting vLLM server for GTX 1080 Ti")
    logger.info(f"Model: {args.model}")
    logger.info(f"Model size: {args.model_size}")
    logger.info(f"Server: http://{args.host}:{args.port}")
    
    # Run the server
    uvicorn.run(
        app,
        host=args.host,
        port=args.port,
        log_level=args.log_level,
        access_log=True
    )


if __name__ == "__main__":
    main()
