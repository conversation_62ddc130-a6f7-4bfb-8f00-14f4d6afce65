# API Setup Guide

This guide will help you set up the required API keys for PimpMyRideAI.

## Required APIs

### 1. ProxyAPI.ru (Image Editing)

ProxyAPI.ru provides access to OpenAI's gpt-image-1 model for editing high-quality car modification images.

#### Setup Steps:

1. **Register at ProxyAPI.ru**
   - Go to [https://console.proxyapi.ru](https://console.proxyapi.ru)
   - Create an account using your email
   - No foreign phone number required

2. **Get API Key**
   - Navigate to [API Keys section](https://console.proxyapi.ru/keys)
   - Create a new API key
   - Copy the key (you can only see it once!)

3. **Add to Environment**
   ```env
   PROXYAPI_KEY=your_proxyapi_key_here
   PROXYAPI_IMAGE_MODEL=gpt-image-1
   ```

#### Features:
- ✅ No VPN required
- ✅ Payment in rubles
- ✅ High-quality image generation
- ✅ gpt-image-1 model support

#### Pricing:
- Check current pricing at [https://proxyapi.ru/pricing](https://proxyapi.ru/pricing)
- Pay-per-use model
- Supports Russian payment methods

### 2. OpenRouter.ai (Image Analysis)

OpenRouter.ai provides access to GPT-4o and GPT-4o-mini models for car recognition and image analysis.

#### Setup Steps:

1. **Register at OpenRouter.ai**
   - Go to [https://openrouter.ai](https://openrouter.ai)
   - Sign up with your email or GitHub

2. **Get API Key**
   - Go to [Settings > Credits](https://openrouter.ai/settings/credits)
   - Create a new API key
   - Copy the key

3. **Add to Environment**
   ```env
   OPENROUTER_API_KEY=your_openrouter_api_key_here
   OPENROUTER_MODEL_VISION=openai/gpt-4o
   OPENROUTER_MODEL_TEXT=openai/gpt-4o-mini
   ```

#### Features:
- ✅ Access to multiple AI models
- ✅ Competitive pricing
- ✅ High-quality vision models
- ✅ Reliable API

#### Pricing:
- Check current pricing at [https://openrouter.ai/models](https://openrouter.ai/models)
- Pay-per-token model
- Various payment methods supported

## Environment Configuration

Create a `.env` file in your project root:

```env
# Telegram Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# ProxyAPI Configuration (for image editing)
PROXYAPI_KEY=your_proxyapi_key_here
PROXYAPI_IMAGE_MODEL=gpt-image-1

# OpenRouter Configuration (for image analysis)
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_MODEL_VISION=openai/gpt-4o
OPENROUTER_MODEL_TEXT=openai/gpt-4o-mini

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/car_modification_db

# Server Configuration
PORT=3000
NODE_ENV=development

# File Configuration
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760
```

## Testing Your Setup

After setting up your API keys, test the configuration:

```bash
# Test API connections
npm run test:apis

# Test with a specific car image
npm run test:apis https://example.com/car-image.jpg
```

## Troubleshooting

### ProxyAPI Issues

1. **Invalid API Key**
   - Verify the key is correct
   - Check if the key has sufficient balance
   - Ensure no extra spaces in the .env file

2. **Connection Timeout**
   - Image generation can take up to 2 minutes
   - Check your internet connection
   - Verify ProxyAPI.ru is accessible

3. **Rate Limits**
   - Check your account limits
   - Consider upgrading your plan

### OpenRouter Issues

1. **Authentication Failed**
   - Verify the API key is correct
   - Check if the key has sufficient credits
   - Ensure the key has the right permissions

2. **Model Not Available**
   - Check if the model is still available
   - Try alternative models (gpt-4o-mini instead of gpt-4o)
   - Verify model names are correct

3. **Rate Limits**
   - Check your usage limits
   - Consider upgrading your plan
   - Implement retry logic if needed

## Cost Optimization Tips

### ProxyAPI.ru
- Use appropriate quality settings (low/medium/high)
- Optimize image prompts for better results
- Monitor usage in the console

### OpenRouter.ai
- Use gpt-4o-mini for text processing when possible
- Optimize image detail settings (low/high)
- Cache recognition results when appropriate

## Security Best Practices

1. **Environment Variables**
   - Never commit API keys to version control
   - Use different keys for development/production
   - Rotate keys regularly

2. **Access Control**
   - Limit API key permissions when possible
   - Monitor usage for unusual activity
   - Set up billing alerts

3. **Error Handling**
   - Don't expose API keys in error messages
   - Log errors securely
   - Implement proper retry mechanisms

## Support

### ProxyAPI.ru Support
- Documentation: [https://proxyapi.ru/docs](https://proxyapi.ru/docs)
- Contact: [https://proxyapi.ru/contact](https://proxyapi.ru/contact)

### OpenRouter.ai Support
- Documentation: [https://openrouter.ai/docs](https://openrouter.ai/docs)
- Discord: [https://discord.gg/fVyRaUDgxW](https://discord.gg/fVyRaUDgxW)

## Next Steps

After setting up your APIs:

1. Run the test script to verify everything works
2. Start the development server: `npm run dev`
3. Test the Telegram bot with real car images
4. Monitor usage and costs in both platforms
5. Consider setting up monitoring and alerts
