# Интеграция с внешними API автозапчастей

Этот документ описывает интеграцию PimpMyRideAI с внешними API для получения актуальных данных о запчастях и ценах.

## 🔗 Поддерживаемые API

### 1. ABCP.ru (Рекомендуется)

**Описание**: Крупнейшая B2B платформа автозапчастей в России

**Преимущества**:
- Огромная база поставщиков (более 1000)
- Актуальные цены и остатки в реальном времени
- Кроссы и аналоги запчастей
- Хорошо документированное API
- Поддержка поиска по VIN, OEM номерам

**Настройка**:
1. Зарегистрируйтесь на https://www.abcp.ru/
2. Подайте заявку на получение API ключа
3. Добавьте `ABCP_API_KEY` в .env файл

**Документация**: https://www.abcp.ru/wiki/API

### 2. Exist.ru

**Описание**: Крупнейший интернет-магазин автозапчастей для иномарок

**Преимущества**:
- Специализация на иномарках
- Широкий ассортимент
- Быстрая доставка по России

**Настройка**:
1. Свяжитесь с Exist.ru для получения партнерского доступа
2. Добавьте `EXIST_API_KEY` в .env файл

**Примечание**: API документация может отличаться, требуется уточнение у поставщика

### 3. AutoDoc (Европейский рынок)

**Описание**: Европейский поставщик автозапчастей

**Преимущества**:
- Оригинальные запчасти
- Европейские бренды
- Международная доставка

**Настройка**:
1. Зарегистрируйтесь на AutoDoc
2. Получите API ключ
3. Добавьте `AUTODOC_API_KEY` в .env файл

## 🛠️ Техническая реализация

### Архитектура

```
PricingService
├── AbcpService (основной)
├── ExistService (резервный)
├── AutodocService (для европейских авто)
└── Fallback (внутренняя база)
```

### Алгоритм работы

1. **Поиск в ABCP** - основной источник данных
2. **Поиск в Exist.ru** - если ABCP не дал результатов
3. **Поиск в AutoDoc** - для европейских марок
4. **Внутренняя база** - если все API недоступны

### Кэширование

- Результаты кэшируются на 30 минут
- Снижает нагрузку на внешние API
- Ускоряет повторные запросы

## 📊 Примеры использования

### Поиск запчастей

```typescript
// Поиск обвеса для BMW
const result = await pricingService.getModificationPricing(
  {
    make: 'BMW',
    model: '3 Series',
    year: 2020,
    bodyType: 'sedan'
  },
  'sport body kit'
);

console.log(result);
// {
//   specificPart: 'M Performance Body Kit',
//   brand: 'BMW',
//   partNumber: '51192365468',
//   price: 85000,
//   installationTime: '6-8 hours',
//   description: 'Original BMW M Performance body kit...'
// }
```

### Сравнение цен

```typescript
const comparison = await pricingService.getPriceComparison(
  carInfo,
  'sport wheels'
);

// Возвращает массив вариантов от разных поставщиков
```

## 🔧 Настройка и тестирование

### Переменные окружения

```env
# Основные API
ABCP_API_KEY=your_abcp_key_here
EXIST_API_KEY=your_exist_key_here
AUTODOC_API_KEY=your_autodoc_key_here

# Кэширование
CACHE_TTL=1800  # 30 минут
```

### Тестирование API

```bash
# Запуск тестов API
npm run test:apis

# Проверка доступности API
npm run health-check
```

## 📈 Мониторинг и аналитика

### Метрики

- Время ответа API
- Процент успешных запросов
- Использование кэша
- Стоимость запросов

### Логирование

Все запросы к внешним API логируются с уровнем INFO:

```
[INFO] Searching ABCP for: BMW 3 Series body kit
[INFO] Retrieved external pricing for sport body kit: $1200
[WARN] ABCP API health check failed: timeout
```

## 🚨 Обработка ошибок

### Стратегия Fallback

1. **Таймаут API** → переход к следующему источнику
2. **Ошибка авторизации** → уведомление администратора
3. **Лимит запросов** → использование кэша
4. **Все API недоступны** → внутренняя база данных

### Уведомления

При критических ошибках система отправляет уведомления:
- Telegram администратору
- Email уведомления
- Slack интеграция (опционально)

## 💰 Стоимость и лимиты

### ABCP.ru
- Стоимость: от 0.01₽ за запрос
- Лимит: 10,000 запросов/день (базовый тариф)
- Превышение: автоматическое отключение

### Exist.ru
- Стоимость: договорная
- Лимит: согласно договору

### Рекомендации по оптимизации

1. **Используйте кэширование** - снижает количество запросов на 70%
2. **Группируйте запросы** - один запрос для нескольких модификаций
3. **Мониторьте лимиты** - настройте алерты при приближении к лимиту

## 🔮 Планы развития

### Ближайшие планы
- [ ] Интеграция с ZZap.ru
- [ ] Поддержка EMEX API
- [ ] Автоматическое обновление цен
- [ ] Система скидок и акций

### Долгосрочные планы
- [ ] Машинное обучение для предсказания цен
- [ ] Интеграция с международными поставщиками
- [ ] API для партнеров
- [ ] Мобильное приложение для поставщиков
