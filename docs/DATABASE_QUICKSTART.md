# 🚀 Быстрый старт базы данных PimpMyRideAI

## ⚡ Автоматическая настройка (рекомендуется)

### Для Linux/macOS:
```bash
# Запустите автоматический скрипт настройки
./scripts/setup-database.sh
```

### Для Windows:
```bash
# Установите PostgreSQL вручную, затем:
npm run db:generate
npm run db:push
npm run db:seed
```

## 🔧 Ручная настройка

### 1. Создание базы данных
```sql
-- Подключитесь к PostgreSQL
psql -U postgres

-- Создайте базу данных
CREATE DATABASE car_modifications;

-- Создайте пользователя
CREATE USER pimpmyride WITH PASSWORD 'your_password';

-- Предоставьте права
GRANT ALL PRIVILEGES ON DATABASE car_modifications TO pimpmyride;
```

### 2. Настройка .env файла
```env
DATABASE_URL="postgresql://pimpmyride:your_password@localhost:5432/car_modifications"
```

### 3. Применение схемы
```bash
# Генерация клиента
npm run db:generate

# Создание таблиц
npm run db:push

# Заполнение данными
npm run db:seed
```

## 📊 Проверка состояния

```bash
# Проверка подключения и структуры
npm run db:check

# Веб-интерфейс для просмотра данных
npm run db:studio
```

## 🗂️ Структура данных

### Основные таблицы:
- **users** - Пользователи Telegram
- **user_sessions** - Активные сессии
- **cars** - Справочник автомобилей
- **modification_categories** - Категории модификаций
- **modification_parts** - Каталог запчастей
- **modification_history** - История модификаций

### Начальные данные включают:
- 8 категорий модификаций
- 20 популярных автомобилей
- 10 базовых запчастей

## 🛠️ Полезные команды

```bash
# Управление базой данных
npm run db:generate    # Генерация Prisma клиента
npm run db:push        # Применение схемы
npm run db:migrate     # Создание миграции
npm run db:seed        # Заполнение данными
npm run db:check       # Проверка состояния
npm run db:reset       # Полный сброс
npm run db:studio      # Веб-интерфейс

# Прямое подключение к базе
psql -U pimpmyride -d car_modifications
```

## 🔍 Устранение проблем

### Ошибка подключения:
```bash
# Проверьте статус PostgreSQL
sudo systemctl status postgresql  # Linux
brew services list | grep postgresql  # macOS

# Проверьте порт
netstat -tlnp | grep 5432
```

### Ошибки прав доступа:
```sql
-- Предоставьте права заново
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO pimpmyride;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO pimpmyride;
```

### Проблемы с миграциями:
```bash
# Сброс состояния
npm run db:reset

# Принудительное применение
npx prisma db push --force-reset
```

## 📈 Мониторинг

### Просмотр статистики:
```sql
-- Количество записей в таблицах
SELECT 
    'users' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'cars', COUNT(*) FROM cars
UNION ALL
SELECT 'modifications', COUNT(*) FROM modification_history;
```

### Размер базы данных:
```sql
SELECT 
    pg_size_pretty(pg_database_size('car_modifications')) as database_size;
```

## 🎯 Следующие шаги

После успешной настройки базы данных:

1. **Настройте API ключи** в .env файле
2. **Запустите бота**: `npm run dev`
3. **Протестируйте** функциональность
4. **Настройте мониторинг** для продакшна

## 📚 Дополнительная документация

- [Полное руководство по базе данных](DATABASE_GUIDE.md)
- [Настройка API](API_SETUP.md)
- [Общее руководство по настройке](SETUP_GUIDE.md)
