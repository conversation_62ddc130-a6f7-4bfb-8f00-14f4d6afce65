# vLLM Setup Guide for NVIDIA GTX 1080 Ti

This guide provides comprehensive instructions for setting up vLLM (Vector Large Language Model serving framework) on an NVIDIA GeForce GTX 1080 Ti with 11GB VRAM.

## Table of Contents

1. [Hardware Requirements](#hardware-requirements)
2. [Prerequisites](#prerequisites)
3. [CUDA Setup](#cuda-setup)
4. [Python Environment Setup](#python-environment-setup)
5. [vLLM Installation](#vllm-installation)
6. [Model Selection](#model-selection)
7. [Configuration](#configuration)
8. [Performance Optimization](#performance-optimization)
9. [Troubleshooting](#troubleshooting)

## Hardware Requirements

### Your Hardware
- **GPU**: NVIDIA GeForce GTX 1080 Ti
- **VRAM**: 11GB GDDR5X
- **Architecture**: Pascal (Compute Capability 6.1)
- **CUDA Cores**: 3584

### Compatibility Notes
- ✅ **CUDA Support**: Full support (Pascal architecture)
- ✅ **vLLM Compatibility**: Supported with CUDA 11.8+
- ⚠️ **Memory Limitation**: 11GB VRAM limits model size
- ⚠️ **Tensor Cores**: Not available (introduced in Volta/Turing)

## Prerequisites

### System Requirements
- **OS**: Linux (Ubuntu 20.04+ recommended) or Windows 10/11
- **RAM**: 16GB+ system RAM recommended
- **Storage**: 50GB+ free space for models and dependencies
- **Python**: 3.8-3.11 (3.10 recommended)

### Driver Requirements
- **NVIDIA Driver**: 470.57.02+ (latest recommended)
- **CUDA**: 11.8 or 12.1 (12.1 recommended for best performance)
- **cuDNN**: 8.x (automatically installed with PyTorch)

## CUDA Setup

### Option 1: CUDA 12.1 (Recommended)

#### Linux (Ubuntu/Debian)
```bash
# Remove old CUDA installations
sudo apt-get --purge remove "*cublas*" "*cufft*" "*curand*" "*cusolver*" "*cusparse*" "*npp*" "*nvjpeg*" "cuda*" "nsight*"

# Add NVIDIA package repository
wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2004/x86_64/cuda-keyring_1.0-1_all.deb
sudo dpkg -i cuda-keyring_1.0-1_all.deb
sudo apt-get update

# Install CUDA 12.1
sudo apt-get install cuda-12-1

# Add to PATH
echo 'export PATH=/usr/local/cuda-12.1/bin:$PATH' >> ~/.bashrc
echo 'export LD_LIBRARY_PATH=/usr/local/cuda-12.1/lib64:$LD_LIBRARY_PATH' >> ~/.bashrc
source ~/.bashrc
```

#### Windows
1. Download CUDA 12.1 from [NVIDIA Developer](https://developer.nvidia.com/cuda-12-1-0-download-archive)
2. Run the installer with default settings
3. Verify installation: `nvcc --version`

### Verification
```bash
# Check CUDA version
nvcc --version

# Check GPU status
nvidia-smi

# Test CUDA with Python
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}'); print(f'GPU: {torch.cuda.get_device_name(0)}')"
```

## Python Environment Setup

### Create Virtual Environment
```bash
# Using conda (recommended)
conda create -n vllm python=3.10
conda activate vllm

# Or using venv
python -m venv vllm-env
source vllm-env/bin/activate  # Linux/Mac
# vllm-env\Scripts\activate  # Windows
```

### Install Base Dependencies
```bash
# Upgrade pip
pip install --upgrade pip

# Install PyTorch with CUDA support
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# Verify PyTorch CUDA
python -c "import torch; print(f'PyTorch version: {torch.__version__}'); print(f'CUDA available: {torch.cuda.is_available()}')"
```

## vLLM Installation

### Install vLLM
```bash
# Install vLLM with CUDA support
pip install vllm

# Alternative: Install from source for latest features
# git clone https://github.com/vllm-project/vllm.git
# cd vllm
# pip install -e .
```

### Verify Installation
```bash
python -c "import vllm; print(f'vLLM version: {vllm.__version__}')"
```

## Model Selection

### Recommended Models for 11GB VRAM

#### Small Models (2-8GB VRAM)
- **Llama 2 7B**: ~7GB VRAM
- **Mistral 7B**: ~7GB VRAM
- **CodeLlama 7B**: ~7GB VRAM
- **Phi-3 Mini (3.8B)**: ~4GB VRAM

#### Medium Models (8-11GB VRAM)
- **Llama 2 7B Chat (4-bit)**: ~4GB VRAM
- **Mistral 7B Instruct (4-bit)**: ~4GB VRAM
- **Llama 2 13B (4-bit quantized)**: ~8GB VRAM

#### Quantization Options
- **4-bit (GPTQ/AWQ)**: ~25% of original size
- **8-bit**: ~50% of original size
- **16-bit (FP16)**: ~50% of original size (from FP32)

### Model Download Examples
```bash
# Download models using Hugging Face
pip install huggingface_hub

# Example: Download Mistral 7B
python -c "
from huggingface_hub import snapshot_download
snapshot_download('mistralai/Mistral-7B-Instruct-v0.1', local_dir='./models/mistral-7b-instruct')
"
```

## Configuration

### Basic vLLM Server Configuration
```python
# config/vllm_config.py
from vllm import LLM, SamplingParams

# GTX 1080 Ti optimized configuration
VLLM_CONFIG = {
    "model": "mistralai/Mistral-7B-Instruct-v0.1",
    "tensor_parallel_size": 1,  # Single GPU
    "gpu_memory_utilization": 0.85,  # Use 85% of VRAM
    "max_model_len": 4096,  # Reduce for memory efficiency
    "dtype": "float16",  # Use FP16 for memory efficiency
    "quantization": None,  # Set to "awq" or "gptq" for quantized models
    "seed": 42,
    "trust_remote_code": True
}

# Sampling parameters
SAMPLING_PARAMS = SamplingParams(
    temperature=0.7,
    top_p=0.9,
    max_tokens=512,
    stop=["</s>", "[INST]", "[/INST]"]
)
```

### Memory Optimization Settings
```python
# For maximum memory efficiency
MEMORY_OPTIMIZED_CONFIG = {
    "model": "microsoft/Phi-3-mini-4k-instruct",
    "tensor_parallel_size": 1,
    "gpu_memory_utilization": 0.95,  # Aggressive memory usage
    "max_model_len": 2048,  # Shorter context
    "dtype": "float16",
    "swap_space": 4,  # 4GB CPU swap space
    "cpu_offload_gb": 2,  # Offload 2GB to CPU
}
```

## Performance Optimization

### GPU-Specific Optimizations
```bash
# Set optimal GPU clocks (Linux)
sudo nvidia-smi -pm 1  # Enable persistence mode
sudo nvidia-smi -ac 5505,1911  # Set memory and GPU clocks for GTX 1080 Ti

# Set power limit to maximum
sudo nvidia-smi -pl 300  # 300W for GTX 1080 Ti
```

### Environment Variables
```bash
# Add to ~/.bashrc or set before running
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
export VLLM_USE_MODELSCOPE=False
export VLLM_WORKER_MULTIPROC_METHOD=spawn
```

### Batch Size Optimization
```python
# Optimal batch sizes for GTX 1080 Ti
BATCH_CONFIGS = {
    "7B_models": {
        "max_num_seqs": 16,  # Concurrent sequences
        "max_num_batched_tokens": 2048,
    },
    "3B_models": {
        "max_num_seqs": 32,
        "max_num_batched_tokens": 4096,
    }
}
```

## Next Steps

1. **Environment Setup**: Run the Python environment setup script
2. **Server Implementation**: Set up the vLLM server with optimizations
3. **Client Integration**: Create client code for your application
4. **Performance Tuning**: Fine-tune settings for your specific use case
5. **Monitoring**: Set up monitoring for GPU utilization and performance

## Related Files

- `scripts/setup_vllm_environment.py` - Automated environment setup
- `src/vllm_server.py` - vLLM server implementation
- `examples/vllm_client.py` - Client usage examples
- `docs/VLLM_PERFORMANCE_GUIDE.md` - Detailed performance optimization
- `docs/VLLM_TROUBLESHOOTING.md` - Common issues and solutions

## Support

For issues specific to GTX 1080 Ti:
- Check GPU memory usage: `nvidia-smi`
- Monitor temperatures: `nvidia-smi -q -d temperature`
- vLLM GitHub Issues: https://github.com/vllm-project/vllm/issues
