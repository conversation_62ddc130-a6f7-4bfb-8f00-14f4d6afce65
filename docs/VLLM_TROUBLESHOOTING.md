# vLLM Troubleshooting Guide for GTX 1080 Ti

This guide addresses common issues when running vLLM on the NVIDIA GeForce GTX 1080 Ti and provides solutions specific to Pascal architecture limitations.

## Table of Contents

1. [Installation Issues](#installation-issues)
2. [Memory Problems](#memory-problems)
3. [Performance Issues](#performance-issues)
4. [Compatibility Problems](#compatibility-problems)
5. [Model Loading Issues](#model-loading-issues)
6. [Runtime Errors](#runtime-errors)
7. [Diagnostic Tools](#diagnostic-tools)

## Installation Issues

### CUDA Compatibility Problems

**Problem**: vLLM fails to detect CUDA or reports version mismatch
```
RuntimeError: CUDA error: no kernel image is available for execution on the device
```

**Solutions**:
```bash
# Check CUDA version compatibility
nvcc --version
python -c "import torch; print(torch.version.cuda)"

# Reinstall PyTorch with correct CUDA version
pip uninstall torch torchvision torchaudio
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# Verify CUDA installation
python -c "import torch; print(torch.cuda.is_available()); print(torch.cuda.get_device_name(0))"
```

### vLLM Installation Failures

**Problem**: vLLM installation fails with compilation errors
```
ERROR: Failed building wheel for vllm
```

**Solutions**:
```bash
# Install build dependencies
pip install --upgrade pip setuptools wheel
pip install ninja packaging

# Install with no build isolation
pip install vllm --no-build-isolation

# Alternative: Use pre-built wheels
pip install vllm --find-links https://download.pytorch.org/whl/torch_stable.html

# If still failing, install from source
git clone https://github.com/vllm-project/vllm.git
cd vllm
pip install -e .
```

### Driver Issues

**Problem**: NVIDIA driver not compatible with CUDA version
```
NVIDIA-SMI has failed because it couldn't communicate with the NVIDIA driver
```

**Solutions**:
```bash
# Check driver version
nvidia-smi

# Update to latest driver (Linux)
sudo apt update
sudo apt install nvidia-driver-535  # or latest version

# Windows: Download from NVIDIA website
# Minimum driver version for GTX 1080 Ti: 470.57.02+
```

## Memory Problems

### Out of Memory (OOM) Errors

**Problem**: GPU runs out of memory during model loading
```
torch.cuda.OutOfMemoryError: CUDA out of memory
```

**Solutions**:
```python
# Reduce memory utilization
engine_args = AsyncEngineArgs(
    model="your-model",
    gpu_memory_utilization=0.7,  # Reduce from 0.85
    max_model_len=2048,          # Reduce context length
    swap_space=4,                # Add CPU swap
    cpu_offload_gb=2             # Offload to CPU
)

# Use quantized models
engine_args.quantization = "awq"  # or "gptq"

# Enable memory optimization
import os
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:128"
```

### Memory Fragmentation

**Problem**: Memory fragmentation causes allocation failures
```
RuntimeError: CUDA out of memory. Tried to allocate X MiB (GPU 0; 10.76 GiB total capacity)
```

**Solutions**:
```python
# Clear GPU cache before loading
import torch
torch.cuda.empty_cache()

# Use memory pool optimization
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:128,expandable_segments:True"

# Restart Python process periodically
# Add to your server code:
import gc
gc.collect()
torch.cuda.empty_cache()
```

### System RAM Issues

**Problem**: System runs out of RAM during model loading
```
MemoryError: Unable to allocate X GiB for an array
```

**Solutions**:
```bash
# Increase swap space
sudo fallocate -l 8G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# Monitor memory usage
free -h
htop

# Use smaller models or quantization
```

## Performance Issues

### Low GPU Utilization

**Problem**: GPU utilization below 50% during inference
```
nvidia-smi shows low GPU usage
```

**Solutions**:
```python
# Increase batch size
engine_args = AsyncEngineArgs(
    max_num_seqs=32,              # Increase concurrent sequences
    max_num_batched_tokens=4096,  # Increase batch tokens
)

# Optimize sampling parameters
sampling_params = SamplingParams(
    top_k=50,  # Limit vocabulary for speed
    max_tokens=256  # Shorter responses
)

# Check for CPU bottlenecks
import psutil
print(f"CPU usage: {psutil.cpu_percent()}%")
```

### Slow Token Generation

**Problem**: Very slow tokens per second (< 10 tokens/s)
```
Expected 20+ tokens/s, getting 5 tokens/s
```

**Solutions**:
```bash
# Check GPU clocks
nvidia-smi -q -d clock

# Set performance mode
sudo nvidia-smi -pm 1
sudo nvidia-smi -ac 5505,1911

# Check thermal throttling
nvidia-smi -q -d temperature

# Verify model size
python -c "
import torch
model = torch.load('model.bin', map_location='cpu')
print(f'Model size: {sum(p.numel() for p in model.parameters()) / 1e9:.1f}B parameters')
"
```

### High Latency

**Problem**: Long delay before first token
```
Time to first token > 5 seconds
```

**Solutions**:
```python
# Reduce context length
engine_args.max_model_len = 1024

# Use smaller models
# Switch from 7B to 3B model

# Optimize batch processing
engine_args.max_num_seqs = 1  # For single requests

# Warm up the model
await engine.generate("Hello", SamplingParams(max_tokens=1), "warmup")
```

## Compatibility Problems

### Pascal Architecture Limitations

**Problem**: Features not supported on GTX 1080 Ti
```
RuntimeError: Tensor cores not available on this device
```

**Solutions**:
```python
# Disable tensor core optimizations
engine_args = AsyncEngineArgs(
    dtype="float16",  # Use FP16 instead of BF16
    # Don't use tensor_parallel_size > 1
    tensor_parallel_size=1
)

# Avoid BF16 (not supported on Pascal)
# Use FP16 instead
```

### CUDA Compute Capability

**Problem**: Model requires newer compute capability
```
RuntimeError: CUDA capability sm_61 is not compatible with the current PyTorch installation
```

**Solutions**:
```bash
# Check compute capability
python -c "
import torch
if torch.cuda.is_available():
    props = torch.cuda.get_device_properties(0)
    print(f'Compute capability: {props.major}.{props.minor}')
"

# GTX 1080 Ti has compute capability 6.1
# Ensure PyTorch supports sm_61
pip install torch --index-url https://download.pytorch.org/whl/cu121
```

## Model Loading Issues

### Model Download Failures

**Problem**: Hugging Face model download fails
```
OSError: We couldn't connect to 'https://huggingface.co' to load this model
```

**Solutions**:
```bash
# Set up authentication
huggingface-cli login

# Use local model path
python vllm_server.py --model /path/to/local/model

# Download manually
git lfs install
git clone https://huggingface.co/mistralai/Mistral-7B-Instruct-v0.1

# Set cache directory
export HF_HOME=/path/to/cache
```

### Quantized Model Issues

**Problem**: Quantized model fails to load
```
ValueError: Quantization method 'awq' is not supported
```

**Solutions**:
```bash
# Install quantization dependencies
pip install auto-gptq
pip install autoawq

# Verify quantization support
python -c "
import vllm
print('Supported quantizations:', vllm.engine.arg_utils.QUANTIZATION_METHODS)
"

# Use compatible quantized models
# AWQ: TheBloke/Mistral-7B-Instruct-v0.1-AWQ
# GPTQ: TheBloke/Mistral-7B-Instruct-v0.1-GPTQ
```

### Model Format Issues

**Problem**: Unsupported model format
```
ValueError: Model format not supported
```

**Solutions**:
```python
# Convert model to supported format
from transformers import AutoModelForCausalLM, AutoTokenizer

model = AutoModelForCausalLM.from_pretrained("model-name")
tokenizer = AutoTokenizer.from_pretrained("model-name")

model.save_pretrained("./converted-model")
tokenizer.save_pretrained("./converted-model")
```

## Runtime Errors

### Server Startup Failures

**Problem**: vLLM server fails to start
```
RuntimeError: Failed to initialize vLLM engine
```

**Solutions**:
```python
# Check detailed error logs
import logging
logging.basicConfig(level=logging.DEBUG)

# Test minimal configuration
engine_args = AsyncEngineArgs(
    model="microsoft/Phi-3-mini-4k-instruct",  # Small model
    gpu_memory_utilization=0.5,
    max_model_len=1024,
    max_num_seqs=1
)

# Verify GPU availability
import torch
assert torch.cuda.is_available(), "CUDA not available"
assert torch.cuda.device_count() > 0, "No CUDA devices"
```

### Generation Failures

**Problem**: Text generation produces errors
```
RuntimeError: Error in model forward pass
```

**Solutions**:
```python
# Check input format
prompt = "Hello, how are you?"  # Simple prompt
sampling_params = SamplingParams(
    max_tokens=50,
    temperature=0.7,
    top_p=0.9
)

# Validate sampling parameters
assert 0 <= sampling_params.temperature <= 2.0
assert 0 <= sampling_params.top_p <= 1.0
assert sampling_params.max_tokens > 0
```

## Diagnostic Tools

### GPU Diagnostics

```bash
#!/bin/bash
# gpu_diagnostics.sh

echo "=== GPU Diagnostics ==="
echo "NVIDIA Driver:"
nvidia-smi --query-gpu=driver_version --format=csv,noheader

echo -e "\nGPU Information:"
nvidia-smi --query-gpu=name,memory.total,compute_cap --format=csv,noheader

echo -e "\nCUDA Version:"
nvcc --version | grep "release"

echo -e "\nPyTorch CUDA:"
python -c "
import torch
print(f'PyTorch version: {torch.__version__}')
print(f'CUDA available: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'CUDA version: {torch.version.cuda}')
    print(f'GPU: {torch.cuda.get_device_name(0)}')
    print(f'Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB')
"
```

### Memory Diagnostics

```python
#!/usr/bin/env python3
# memory_diagnostics.py

import torch
import psutil
import os

def diagnose_memory():
    print("=== Memory Diagnostics ===")
    
    # System memory
    mem = psutil.virtual_memory()
    print(f"System RAM: {mem.total / 1e9:.1f} GB")
    print(f"Available RAM: {mem.available / 1e9:.1f} GB")
    print(f"RAM usage: {mem.percent}%")
    
    # GPU memory
    if torch.cuda.is_available():
        gpu_mem = torch.cuda.get_device_properties(0).total_memory
        print(f"GPU VRAM: {gpu_mem / 1e9:.1f} GB")
        
        if torch.cuda.memory_allocated() > 0:
            allocated = torch.cuda.memory_allocated() / 1e9
            reserved = torch.cuda.memory_reserved() / 1e9
            print(f"GPU allocated: {allocated:.1f} GB")
            print(f"GPU reserved: {reserved:.1f} GB")
    
    # Swap space
    swap = psutil.swap_memory()
    print(f"Swap total: {swap.total / 1e9:.1f} GB")
    print(f"Swap used: {swap.used / 1e9:.1f} GB")

if __name__ == "__main__":
    diagnose_memory()
```

### Performance Diagnostics

```python
#!/usr/bin/env python3
# performance_diagnostics.py

import time
import subprocess
import torch

def diagnose_performance():
    print("=== Performance Diagnostics ===")
    
    # GPU clocks
    try:
        result = subprocess.run([
            "nvidia-smi", "--query-gpu=clocks.gr,clocks.mem,temperature.gpu,power.draw",
            "--format=csv,noheader,nounits"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            clocks = result.stdout.strip().split(',')
            print(f"GPU Clock: {clocks[0]} MHz")
            print(f"Memory Clock: {clocks[1]} MHz") 
            print(f"Temperature: {clocks[2]}°C")
            print(f"Power Draw: {clocks[3]}W")
    except:
        print("Could not get GPU clocks")
    
    # Simple CUDA benchmark
    if torch.cuda.is_available():
        print("\nCUDA Performance Test:")
        device = torch.device("cuda")
        
        # Matrix multiplication benchmark
        size = 2048
        a = torch.randn(size, size, device=device, dtype=torch.float16)
        b = torch.randn(size, size, device=device, dtype=torch.float16)
        
        # Warmup
        for _ in range(5):
            c = torch.matmul(a, b)
        
        torch.cuda.synchronize()
        start_time = time.time()
        
        for _ in range(10):
            c = torch.matmul(a, b)
        
        torch.cuda.synchronize()
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 10
        tflops = (2 * size**3) / (avg_time * 1e12)
        print(f"Matrix multiplication: {avg_time*1000:.1f}ms")
        print(f"Performance: {tflops:.1f} TFLOPS")

if __name__ == "__main__":
    diagnose_performance()
```

### Quick Health Check

```bash
#!/bin/bash
# quick_health_check.sh

echo "=== vLLM Health Check ==="

# Check NVIDIA driver
if command -v nvidia-smi &> /dev/null; then
    echo "✅ NVIDIA driver installed"
    nvidia-smi --query-gpu=name --format=csv,noheader
else
    echo "❌ NVIDIA driver not found"
    exit 1
fi

# Check CUDA
if command -v nvcc &> /dev/null; then
    echo "✅ CUDA toolkit installed"
    nvcc --version | grep "release"
else
    echo "❌ CUDA toolkit not found"
fi

# Check Python packages
python -c "
try:
    import torch
    print('✅ PyTorch installed:', torch.__version__)
    print('✅ CUDA available:', torch.cuda.is_available())
except ImportError:
    print('❌ PyTorch not installed')

try:
    import vllm
    print('✅ vLLM installed:', vllm.__version__)
except ImportError:
    print('❌ vLLM not installed')
"

echo "Health check complete!"
```

This troubleshooting guide covers the most common issues encountered when running vLLM on the GTX 1080 Ti. For additional support, check the vLLM GitHub issues or community forums.
