# 🗄️ Руководство по базе данных PimpMyRideAI

## 📊 Обзор архитектуры

База данных PimpMyRideAI построена на PostgreSQL с использованием Prisma ORM и содержит 8 основных таблиц для хранения информации о пользователях, автомобилях, модификациях и истории операций.

## 🏗️ Структура базы данных

### Основные таблицы:

1. **`users`** - Пользователи Telegram
2. **`user_sessions`** - Активные сессии пользователей
3. **`cars`** - Справочник автомобилей
4. **`modification_categories`** - Категории модификаций
5. **`modification_parts`** - Каталог запчастей
6. **`modification_history`** - История модификаций
7. **`price_history`** - История цен
8. **`api_usage`** - Статистика использования API

## 🔧 Настройка базы данных

### Шаг 1: Установка PostgreSQL

**Windows:**
```bash
# Скачайте с https://www.postgresql.org/download/windows/
# Запустите установщик и запомните пароль для postgres
```

**macOS:**
```bash
brew install postgresql@14
brew services start postgresql@14
```

**Linux:**
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### Шаг 2: Создание базы данных

```sql
-- Подключение к PostgreSQL
psql -U postgres

-- Создание базы данных
CREATE DATABASE car_modifications;

-- Создание пользователя
CREATE USER pimpmyride WITH PASSWORD 'your_secure_password';

-- Предоставление прав
GRANT ALL PRIVILEGES ON DATABASE car_modifications TO pimpmyride;

-- Подключение к базе
\c car_modifications;

-- Права на схему
GRANT ALL ON SCHEMA public TO pimpmyride;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO pimpmyride;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO pimpmyride;
```

### Шаг 3: Настройка переменных окружения

```env
# .env файл
DATABASE_URL="postgresql://pimpmyride:your_password@localhost:5432/car_modifications"

# Альтернативные варианты:
# DATABASE_URL="postgresql://postgres:postgres_password@localhost:5432/car_modifications"
# DATABASE_URL="**********************************/car_modifications" # для Docker
```

### Шаг 4: Применение схемы

```bash
# Генерация Prisma клиента
npm run db:generate

# Применение схемы к базе данных
npm run db:push

# Заполнение начальными данными
npm run db:seed

# Проверка состояния базы данных
npm run db:check
```

## 📋 Детальное описание таблиц

### 1. Таблица `users`

```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    telegram_id BIGINT UNIQUE NOT NULL,
    username VARCHAR(255),
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255),
    language_code VARCHAR(10),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

**Назначение:** Хранение информации о пользователях Telegram

**Ключевые поля:**
- `telegram_id` - Уникальный ID пользователя в Telegram
- `username` - Username в Telegram (может отсутствовать)
- `first_name` - Имя пользователя
- `language_code` - Код языка пользователя

### 2. Таблица `user_sessions`

```sql
CREATE TABLE user_sessions (
    id VARCHAR(25) PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    chat_id BIGINT NOT NULL,
    current_step VARCHAR(50) NOT NULL,
    uploaded_image VARCHAR(255),
    car_info JSONB,
    last_activity TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW()
);
```

**Назначение:** Управление состоянием диалога с пользователем

**Ключевые поля:**
- `current_step` - Текущий шаг в диалоге (waiting_for_image, car_recognized, etc.)
- `uploaded_image` - ID загруженного изображения в Telegram
- `car_info` - JSON с информацией о распознанном автомобиле

**Возможные значения `current_step`:**
- `waiting_for_image` - Ожидание загрузки фото
- `processing_image` - Обработка изображения
- `car_recognized` - Автомобиль распознан
- `waiting_for_modifications` - Ожидание запроса модификаций
- `generating_modifications` - Генерация модификаций
- `showing_results` - Показ результатов

### 3. Таблица `cars`

```sql
CREATE TABLE cars (
    id VARCHAR(25) PRIMARY KEY,
    make VARCHAR(100) NOT NULL,
    model VARCHAR(100) NOT NULL,
    year INTEGER NOT NULL,
    generation VARCHAR(50),
    body_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(make, model, year, generation)
);
```

**Назначение:** Справочник автомобилей

**Ключевые поля:**
- `make` - Марка автомобиля (BMW, Mercedes-Benz, Toyota)
- `model` - Модель (3 Series, C-Class, Camry)
- `year` - Год выпуска
- `generation` - Поколение (G20, W205, XV70)
- `body_type` - Тип кузова (Sedan, SUV, Hatchback, Coupe)

### 4. Таблица `modification_categories`

```sql
CREATE TABLE modification_categories (
    id VARCHAR(25) PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);
```

**Назначение:** Категории модификаций

**Стандартные категории:**
- `body_kit` - Аэродинамические обвесы
- `wheels` - Диски и шины
- `suspension` - Подвеска
- `exhaust` - Выхлопная система
- `lighting` - Освещение
- `interior` - Салон
- `performance` - Производительность
- `exterior` - Внешний тюнинг

### 5. Таблица `modification_parts`

```sql
CREATE TABLE modification_parts (
    id VARCHAR(25) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    category_id VARCHAR(25) REFERENCES modification_categories(id),
    description TEXT NOT NULL,
    average_price DECIMAL(10,2) NOT NULL,
    installation_complexity VARCHAR(20) NOT NULL,
    brand VARCHAR(100),
    part_number VARCHAR(100),
    image_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

**Назначение:** Каталог запчастей и модификаций

**Ключевые поля:**
- `average_price` - Средняя цена в рублях
- `installation_complexity` - Сложность установки (easy, medium, hard, professional)
- `brand` - Бренд производитель
- `part_number` - Артикул запчасти

### 6. Таблица `modification_history`

```sql
CREATE TABLE modification_history (
    id VARCHAR(25) PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    car_id VARCHAR(25) REFERENCES cars(id),
    original_image_url VARCHAR(500) NOT NULL,
    modified_image_url VARCHAR(500) NOT NULL,
    user_request TEXT NOT NULL,
    applied_parts TEXT[] NOT NULL,
    total_cost DECIMAL(10,2) NOT NULL,
    labor_cost DECIMAL(10,2) NOT NULL,
    description TEXT NOT NULL,
    installation_notes TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);
```

**Назначение:** История всех созданных модификаций

**Ключевые поля:**
- `original_image_url` - Ссылка на оригинальное фото
- `modified_image_url` - Ссылка на модифицированное изображение
- `user_request` - Оригинальный запрос пользователя
- `applied_parts` - Массив ID примененных запчастей
- `total_cost` - Общая стоимость
- `labor_cost` - Стоимость работ

## 🛠️ Полезные команды

### Управление базой данных:

```bash
# Генерация Prisma клиента
npm run db:generate

# Применение схемы
npm run db:push

# Создание миграции
npm run db:migrate

# Заполнение данными
npm run db:seed

# Проверка состояния
npm run db:check

# Полный сброс и пересоздание
npm run db:reset

# Веб-интерфейс для просмотра данных
npm run db:studio
```

### Прямые SQL запросы:

```bash
# Подключение к базе
psql -U pimpmyride -d car_modifications

# Просмотр всех таблиц
\dt

# Описание таблицы
\d users

# Просмотр данных
SELECT * FROM users LIMIT 5;
SELECT * FROM modification_categories;
```

## 📊 Мониторинг и обслуживание

### Проверка производительности:

```sql
-- Размер таблиц
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Активные подключения
SELECT count(*) FROM pg_stat_activity;

-- Медленные запросы
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;
```

### Резервное копирование:

```bash
# Создание бэкапа
pg_dump -U pimpmyride -h localhost car_modifications > backup.sql

# Восстановление из бэкапа
psql -U pimpmyride -h localhost car_modifications < backup.sql

# Бэкап только данных
pg_dump -U pimpmyride -h localhost --data-only car_modifications > data_backup.sql
```

## 🔍 Отладка проблем

### Частые проблемы:

1. **Ошибка подключения**
   ```bash
   # Проверка статуса PostgreSQL
   sudo systemctl status postgresql
   
   # Проверка портов
   netstat -tlnp | grep 5432
   ```

2. **Ошибки прав доступа**
   ```sql
   -- Проверка прав пользователя
   \du pimpmyride
   
   -- Предоставление прав
   GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO pimpmyride;
   ```

3. **Проблемы с миграциями**
   ```bash
   # Сброс состояния миграций
   npx prisma migrate reset
   
   # Принудительное применение
   npx prisma db push --force-reset
   ```

## 🚀 Оптимизация

### Рекомендуемые индексы:

```sql
-- Индексы для быстрого поиска
CREATE INDEX idx_users_telegram_id ON users(telegram_id);
CREATE INDEX idx_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_sessions_last_activity ON user_sessions(last_activity);
CREATE INDEX idx_cars_make_model ON cars(make, model);
CREATE INDEX idx_modifications_user_id ON modification_history(user_id);
CREATE INDEX idx_modifications_created_at ON modification_history(created_at);
```

### Настройки PostgreSQL для продакшна:

```sql
-- postgresql.conf
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
```

Эта база данных обеспечивает полную функциональность PimpMyRideAI бота, включая управление пользователями, сессиями, каталогом автомобилей и запчастей, а также историей всех модификаций.
