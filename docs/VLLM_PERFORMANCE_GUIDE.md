# vLLM Performance Optimization Guide for GTX 1080 Ti

This guide provides detailed performance optimization strategies specifically for running vLLM on the NVIDIA GeForce GTX 1080 Ti.

## Table of Contents

1. [Hardware Optimization](#hardware-optimization)
2. [Memory Management](#memory-management)
3. [Model Selection and Quantization](#model-selection-and-quantization)
4. [Configuration Tuning](#configuration-tuning)
5. [System-Level Optimizations](#system-level-optimizations)
6. [Monitoring and Profiling](#monitoring-and-profiling)
7. [Performance Benchmarks](#performance-benchmarks)

## Hardware Optimization

### GPU Clock Settings

The GTX 1080 Ti can be overclocked for better performance:

```bash
# Enable persistence mode (Linux)
sudo nvidia-smi -pm 1

# Set maximum power limit (300W for GTX 1080 Ti)
sudo nvidia-smi -pl 300

# Set optimal memory and GPU clocks
sudo nvidia-smi -ac 5505,1911  # Memory: 5505 MHz, GPU: 1911 MHz

# Alternative conservative settings
sudo nvidia-smi -ac 5000,1800  # More stable for 24/7 operation
```

### Temperature Management

```bash
# Monitor GPU temperature
nvidia-smi -q -d temperature

# Set aggressive fan curve (if using custom cooling)
nvidia-settings -a "[gpu:0]/GPUFanControlState=1"
nvidia-settings -a "[fan:0]/GPUTargetFanSpeed=80"
```

### Power Management

```bash
# Set performance mode
sudo nvidia-smi -pm 1
echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor
```

## Memory Management

### VRAM Optimization

The GTX 1080 Ti's 11GB VRAM requires careful management:

```python
# Optimal memory configurations by model size
MEMORY_CONFIGS = {
    "3B_models": {
        "gpu_memory_utilization": 0.9,
        "max_model_len": 8192,
        "max_num_seqs": 32,
        "max_num_batched_tokens": 4096,
        "swap_space": 2,  # GB
    },
    "7B_models": {
        "gpu_memory_utilization": 0.85,
        "max_model_len": 4096,
        "max_num_seqs": 16,
        "max_num_batched_tokens": 2048,
        "swap_space": 4,  # GB
    },
    "13B_quantized": {
        "gpu_memory_utilization": 0.95,
        "max_model_len": 2048,
        "max_num_seqs": 8,
        "max_num_batched_tokens": 1024,
        "swap_space": 6,  # GB
        "cpu_offload_gb": 2,
    }
}
```

### Memory Fragmentation Prevention

```python
# Environment variables for memory management
import os
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:128,expandable_segments:True"
os.environ["CUDA_LAUNCH_BLOCKING"] = "0"  # Async kernel launches
```

### System RAM Requirements

```bash
# Recommended system RAM by model size
# 3B models: 16GB system RAM
# 7B models: 24GB system RAM  
# 13B models: 32GB system RAM

# Check current memory usage
free -h
cat /proc/meminfo | grep -E "(MemTotal|MemAvailable|SwapTotal)"
```

## Model Selection and Quantization

### Recommended Models for GTX 1080 Ti

#### Optimal Models (7-9GB VRAM usage)
```python
RECOMMENDED_MODELS = {
    "mistral_7b": {
        "model": "mistralai/Mistral-7B-Instruct-v0.1",
        "vram_usage": "~7GB",
        "performance": "Excellent",
        "use_case": "General purpose, coding"
    },
    "llama2_7b": {
        "model": "meta-llama/Llama-2-7b-chat-hf",
        "vram_usage": "~7GB", 
        "performance": "Excellent",
        "use_case": "Conversational AI"
    },
    "codellama_7b": {
        "model": "codellama/CodeLlama-7b-Instruct-hf",
        "vram_usage": "~7GB",
        "performance": "Excellent", 
        "use_case": "Code generation"
    },
    "phi3_mini": {
        "model": "microsoft/Phi-3-mini-4k-instruct",
        "vram_usage": "~4GB",
        "performance": "Very Good",
        "use_case": "Fast inference, mobile"
    }
}
```

#### Quantized Models (4-6GB VRAM usage)
```python
QUANTIZED_MODELS = {
    "mistral_7b_awq": {
        "model": "TheBloke/Mistral-7B-Instruct-v0.1-AWQ",
        "quantization": "awq",
        "vram_usage": "~4GB",
        "performance": "Good (minimal quality loss)"
    },
    "llama2_13b_gptq": {
        "model": "TheBloke/Llama-2-13B-chat-GPTQ",
        "quantization": "gptq", 
        "vram_usage": "~8GB",
        "performance": "Very Good"
    }
}
```

### Quantization Configuration

```python
# AWQ Quantization (recommended)
engine_args = AsyncEngineArgs(
    model="TheBloke/Mistral-7B-Instruct-v0.1-AWQ",
    quantization="awq",
    dtype="float16",
    gpu_memory_utilization=0.9,
    max_model_len=4096
)

# GPTQ Quantization
engine_args = AsyncEngineArgs(
    model="TheBloke/Llama-2-13B-chat-GPTQ", 
    quantization="gptq",
    dtype="float16",
    gpu_memory_utilization=0.95,
    max_model_len=2048
)
```

## Configuration Tuning

### Batch Size Optimization

```python
def get_optimal_batch_config(model_size_gb, available_vram_gb):
    """Calculate optimal batch configuration."""
    memory_ratio = model_size_gb / available_vram_gb
    
    if memory_ratio < 0.4:  # Small model
        return {
            "max_num_seqs": 32,
            "max_num_batched_tokens": 4096,
            "max_model_len": 8192
        }
    elif memory_ratio < 0.7:  # Medium model  
        return {
            "max_num_seqs": 16,
            "max_num_batched_tokens": 2048,
            "max_model_len": 4096
        }
    else:  # Large model
        return {
            "max_num_seqs": 8,
            "max_num_batched_tokens": 1024,
            "max_model_len": 2048
        }
```

### Context Length Optimization

```python
# Context length vs performance trade-offs
CONTEXT_CONFIGS = {
    "speed_optimized": {
        "max_model_len": 1024,
        "description": "Fastest inference, short conversations"
    },
    "balanced": {
        "max_model_len": 2048, 
        "description": "Good balance of speed and context"
    },
    "context_optimized": {
        "max_model_len": 4096,
        "description": "Longer conversations, slower inference"
    },
    "maximum": {
        "max_model_len": 8192,
        "description": "Maximum context (only for small models)"
    }
}
```

### Sampling Parameters

```python
# Performance-optimized sampling
FAST_SAMPLING = SamplingParams(
    temperature=0.7,
    top_p=0.9,
    top_k=50,  # Limit vocabulary for speed
    max_tokens=256,  # Shorter responses
    repetition_penalty=1.1
)

# Quality-optimized sampling  
QUALITY_SAMPLING = SamplingParams(
    temperature=0.8,
    top_p=0.95,
    top_k=-1,  # No vocabulary limit
    max_tokens=512,
    repetition_penalty=1.05,
    frequency_penalty=0.1
)
```

## System-Level Optimizations

### CPU Optimization

```bash
# Set CPU governor to performance
echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor

# Disable CPU idle states for consistent performance
sudo cpupower idle-set -D 0

# Set CPU affinity for vLLM process
taskset -c 0-7 python vllm_server.py  # Use first 8 cores
```

### I/O Optimization

```bash
# Use faster storage for model cache
export HF_HOME="/path/to/fast/ssd/huggingface"
export TRANSFORMERS_CACHE="/path/to/fast/ssd/transformers"

# Optimize disk scheduler
echo mq-deadline | sudo tee /sys/block/nvme0n1/queue/scheduler
```

### Network Optimization

```python
# FastAPI/Uvicorn optimization
uvicorn.run(
    app,
    host="0.0.0.0",
    port=8000,
    workers=1,  # Single worker for GPU sharing
    loop="uvloop",  # Faster event loop
    http="httptools",  # Faster HTTP parser
    access_log=False,  # Disable for performance
    log_level="warning"
)
```

## Monitoring and Profiling

### GPU Monitoring Script

```python
#!/usr/bin/env python3
import time
import subprocess
import json

def monitor_gpu():
    """Monitor GPU usage during inference."""
    while True:
        try:
            result = subprocess.run([
                "nvidia-smi", "--query-gpu=timestamp,name,temperature.gpu,utilization.gpu,utilization.memory,memory.used,memory.total,power.draw",
                "--format=csv,noheader,nounits"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                data = result.stdout.strip().split(',')
                print(f"Time: {data[0]}")
                print(f"GPU: {data[1]}")
                print(f"Temp: {data[2]}°C")
                print(f"GPU Util: {data[3]}%")
                print(f"Mem Util: {data[4]}%") 
                print(f"Memory: {data[5]}MB / {data[6]}MB")
                print(f"Power: {data[7]}W")
                print("-" * 40)
                
        except Exception as e:
            print(f"Monitoring error: {e}")
            
        time.sleep(1)

if __name__ == "__main__":
    monitor_gpu()
```

### Performance Profiling

```python
import torch.profiler

# Profile vLLM inference
with torch.profiler.profile(
    activities=[
        torch.profiler.ProfilerActivity.CPU,
        torch.profiler.ProfilerActivity.CUDA,
    ],
    schedule=torch.profiler.schedule(wait=1, warmup=1, active=3, repeat=2),
    on_trace_ready=torch.profiler.tensorboard_trace_handler('./log/vllm_profile'),
    record_shapes=True,
    profile_memory=True,
    with_stack=True
) as prof:
    # Run inference
    result = await engine.generate(prompt, sampling_params, request_id)
    prof.step()
```

## Performance Benchmarks

### Expected Performance (GTX 1080 Ti)

```python
PERFORMANCE_BENCHMARKS = {
    "mistral_7b_fp16": {
        "tokens_per_second": "15-25",
        "memory_usage": "7.2GB",
        "batch_size_1": "20 tokens/s",
        "batch_size_8": "120 tokens/s total"
    },
    "mistral_7b_awq": {
        "tokens_per_second": "25-35", 
        "memory_usage": "4.1GB",
        "batch_size_1": "30 tokens/s",
        "batch_size_16": "200 tokens/s total"
    },
    "phi3_mini": {
        "tokens_per_second": "40-60",
        "memory_usage": "3.8GB", 
        "batch_size_1": "50 tokens/s",
        "batch_size_32": "400 tokens/s total"
    }
}
```

### Benchmark Script

```python
async def benchmark_model(engine, num_requests=10):
    """Benchmark model performance."""
    prompt = "Explain machine learning in simple terms:"
    sampling_params = SamplingParams(max_tokens=100, temperature=0.7)
    
    # Warmup
    await engine.generate(prompt, sampling_params, "warmup")
    
    # Benchmark
    start_time = time.time()
    tasks = []
    
    for i in range(num_requests):
        task = engine.generate(prompt, sampling_params, f"req_{i}")
        tasks.append(task)
    
    results = await asyncio.gather(*tasks)
    end_time = time.time()
    
    total_tokens = sum(len(r.outputs[0].token_ids) for r in results)
    total_time = end_time - start_time
    
    print(f"Requests: {num_requests}")
    print(f"Total time: {total_time:.2f}s")
    print(f"Tokens/second: {total_tokens/total_time:.1f}")
    print(f"Requests/second: {num_requests/total_time:.1f}")
```

## Troubleshooting Performance Issues

### Common Issues and Solutions

1. **Low GPU Utilization**
   - Increase batch size (`max_num_seqs`)
   - Reduce context length (`max_model_len`)
   - Use quantized models

2. **Out of Memory Errors**
   - Reduce `gpu_memory_utilization`
   - Decrease `max_model_len`
   - Enable CPU offloading

3. **Slow Token Generation**
   - Check GPU clocks
   - Verify CUDA version compatibility
   - Monitor thermal throttling

4. **High Latency**
   - Reduce batch size for single requests
   - Optimize sampling parameters
   - Use smaller models

### Performance Monitoring Commands

```bash
# Real-time GPU monitoring
watch -n 1 nvidia-smi

# Detailed GPU stats
nvidia-smi dmon -s pucvmet -d 1

# System resource monitoring  
htop
iotop
```

This guide provides comprehensive optimization strategies for maximizing vLLM performance on the GTX 1080 Ti. Regular monitoring and tuning based on your specific workload will yield the best results.
