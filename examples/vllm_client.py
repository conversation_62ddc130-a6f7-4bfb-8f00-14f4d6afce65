#!/usr/bin/env python3
"""
vLLM Client Examples for GTX 1080 Ti Server

This module provides examples of how to interact with the vLLM server
running on GTX 1080 Ti from Python applications.

Features:
- Simple text completion
- Streaming responses
- Batch processing
- Error handling
- Performance monitoring

Usage:
    python vllm_client.py --server-url http://localhost:8000
"""

import asyncio
import json
import time
import argparse
from typing import List, Dict, Optional, AsyncGenerator
import aiohttp
import requests
from dataclasses import dataclass


@dataclass
class CompletionRequest:
    """Request structure for text completion."""
    prompt: str
    max_tokens: int = 512
    temperature: float = 0.7
    top_p: float = 0.9
    top_k: int = -1
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    stop: Optional[List[str]] = None
    stream: bool = False
    seed: Optional[int] = None


class VLLMClient:
    """Client for interacting with vLLM server."""
    
    def __init__(self, server_url: str = "http://localhost:8000"):
        self.server_url = server_url.rstrip('/')
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    def health_check(self) -> bool:
        """Check if the server is healthy."""
        try:
            response = requests.get(f"{self.server_url}/health", timeout=5)
            return response.status_code == 200
        except Exception as e:
            print(f"Health check failed: {e}")
            return False
    
    def get_stats(self) -> Dict:
        """Get server statistics."""
        try:
            response = requests.get(f"{self.server_url}/stats", timeout=5)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Failed to get stats: {e}")
            return {}
    
    def complete(self, request: CompletionRequest) -> Dict:
        """Synchronous text completion."""
        try:
            response = requests.post(
                f"{self.server_url}/v1/completions",
                json=request.__dict__,
                timeout=60
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Completion failed: {e}")
            raise
    
    async def complete_async(self, request: CompletionRequest) -> Dict:
        """Asynchronous text completion."""
        if not self.session:
            raise RuntimeError("Client not initialized. Use async context manager.")
        
        try:
            async with self.session.post(
                f"{self.server_url}/v1/completions",
                json=request.__dict__,
                timeout=aiohttp.ClientTimeout(total=60)
            ) as response:
                response.raise_for_status()
                return await response.json()
        except Exception as e:
            print(f"Async completion failed: {e}")
            raise
    
    async def stream_complete(self, request: CompletionRequest) -> AsyncGenerator[str, None]:
        """Stream text completion."""
        if not self.session:
            raise RuntimeError("Client not initialized. Use async context manager.")
        
        request.stream = True
        
        try:
            async with self.session.post(
                f"{self.server_url}/v1/completions",
                json=request.__dict__,
                timeout=aiohttp.ClientTimeout(total=120)
            ) as response:
                response.raise_for_status()
                
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if line.startswith('data: '):
                        data = line[6:]  # Remove 'data: ' prefix
                        if data == '[DONE]':
                            break
                        try:
                            chunk = json.loads(data)
                            yield chunk.get('text', '')
                        except json.JSONDecodeError:
                            continue
        except Exception as e:
            print(f"Stream completion failed: {e}")
            raise


class VLLMExamples:
    """Collection of usage examples."""
    
    def __init__(self, client: VLLMClient):
        self.client = client
    
    def basic_completion_example(self):
        """Basic text completion example."""
        print("=== Basic Completion Example ===")
        
        request = CompletionRequest(
            prompt="Explain the concept of machine learning in simple terms:",
            max_tokens=200,
            temperature=0.7
        )
        
        start_time = time.time()
        result = self.client.complete(request)
        end_time = time.time()
        
        print(f"Prompt: {request.prompt}")
        print(f"Response: {result['text']}")
        print(f"Tokens: {result['usage']['total_tokens']}")
        print(f"Time: {end_time - start_time:.2f}s")
        print()
    
    async def async_completion_example(self):
        """Asynchronous completion example."""
        print("=== Async Completion Example ===")
        
        request = CompletionRequest(
            prompt="Write a short Python function to calculate fibonacci numbers:",
            max_tokens=300,
            temperature=0.3
        )
        
        start_time = time.time()
        result = await self.client.complete_async(request)
        end_time = time.time()
        
        print(f"Prompt: {request.prompt}")
        print(f"Response: {result['text']}")
        print(f"Tokens: {result['usage']['total_tokens']}")
        print(f"Time: {end_time - start_time:.2f}s")
        print()
    
    async def streaming_example(self):
        """Streaming completion example."""
        print("=== Streaming Example ===")
        
        request = CompletionRequest(
            prompt="Tell me a story about a robot learning to paint:",
            max_tokens=400,
            temperature=0.8
        )
        
        print(f"Prompt: {request.prompt}")
        print("Response (streaming): ", end="", flush=True)
        
        start_time = time.time()
        async for chunk in self.client.stream_complete(request):
            print(chunk, end="", flush=True)
        end_time = time.time()
        
        print(f"\nTime: {end_time - start_time:.2f}s")
        print()
    
    async def batch_processing_example(self):
        """Batch processing example."""
        print("=== Batch Processing Example ===")
        
        prompts = [
            "What is artificial intelligence?",
            "Explain quantum computing briefly.",
            "How does blockchain work?",
            "What is the future of renewable energy?"
        ]
        
        requests = [
            CompletionRequest(prompt=prompt, max_tokens=150, temperature=0.5)
            for prompt in prompts
        ]
        
        start_time = time.time()
        
        # Process batch concurrently
        tasks = [self.client.complete_async(req) for req in requests]
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        
        for i, (prompt, result) in enumerate(zip(prompts, results)):
            print(f"Question {i+1}: {prompt}")
            print(f"Answer: {result['text'][:100]}...")
            print(f"Tokens: {result['usage']['total_tokens']}")
            print()
        
        print(f"Total batch time: {end_time - start_time:.2f}s")
        print(f"Average time per request: {(end_time - start_time) / len(prompts):.2f}s")
        print()
    
    def performance_test(self, num_requests: int = 5):
        """Performance testing example."""
        print(f"=== Performance Test ({num_requests} requests) ===")
        
        request = CompletionRequest(
            prompt="Count from 1 to 10:",
            max_tokens=50,
            temperature=0.1
        )
        
        times = []
        token_counts = []
        
        for i in range(num_requests):
            start_time = time.time()
            result = self.client.complete(request)
            end_time = time.time()
            
            times.append(end_time - start_time)
            token_counts.append(result['usage']['total_tokens'])
            
            print(f"Request {i+1}: {end_time - start_time:.2f}s, {result['usage']['total_tokens']} tokens")
        
        avg_time = sum(times) / len(times)
        avg_tokens = sum(token_counts) / len(token_counts)
        tokens_per_second = avg_tokens / avg_time
        
        print(f"\nPerformance Summary:")
        print(f"Average time: {avg_time:.2f}s")
        print(f"Average tokens: {avg_tokens:.1f}")
        print(f"Tokens per second: {tokens_per_second:.1f}")
        print()


async def main():
    parser = argparse.ArgumentParser(description="vLLM Client Examples")
    parser.add_argument("--server-url", default="http://localhost:8000", 
                       help="vLLM server URL")
    parser.add_argument("--example", choices=[
        "basic", "async", "stream", "batch", "performance", "all"
    ], default="all", help="Which example to run")
    
    args = parser.parse_args()
    
    # Initialize client
    client = VLLMClient(args.server_url)
    
    # Check server health
    print("Checking server health...")
    if not client.health_check():
        print("❌ Server is not healthy. Please start the vLLM server first.")
        return
    
    print("✅ Server is healthy")
    
    # Get server stats
    stats = client.get_stats()
    if stats:
        print(f"Model: {stats.get('model', 'Unknown')}")
        print(f"GPU: {stats.get('gpu_name', 'Unknown')}")
        print(f"GPU Memory: {stats.get('gpu_memory_allocated', 0):.1f}GB / {stats.get('gpu_memory_total', 0):.1f}GB")
        print()
    
    # Run examples
    examples = VLLMExamples(client)
    
    async with client:
        if args.example in ["basic", "all"]:
            examples.basic_completion_example()
        
        if args.example in ["async", "all"]:
            await examples.async_completion_example()
        
        if args.example in ["stream", "all"]:
            await examples.streaming_example()
        
        if args.example in ["batch", "all"]:
            await examples.batch_processing_example()
        
        if args.example in ["performance", "all"]:
            examples.performance_test()


if __name__ == "__main__":
    asyncio.run(main())
