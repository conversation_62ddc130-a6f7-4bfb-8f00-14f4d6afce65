#!/usr/bin/env node
/**
 * vLLM Client Examples for Node.js
 * 
 * This module provides examples of how to interact with the vLLM server
 * running on GTX 1080 Ti from Node.js applications.
 * 
 * Features:
 * - Simple text completion
 * - Streaming responses
 * - Batch processing
 * - Error handling
 * - Performance monitoring
 * 
 * Usage:
 *   node vllm_client.js --server-url http://localhost:8000
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

class VLLMClient {
    constructor(serverUrl = 'http://localhost:8000') {
        this.serverUrl = serverUrl.replace(/\/$/, '');
        this.axiosInstance = axios.create({
            baseURL: this.serverUrl,
            timeout: 60000,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }

    /**
     * Check if the server is healthy
     */
    async healthCheck() {
        try {
            const response = await this.axiosInstance.get('/health');
            return response.status === 200;
        } catch (error) {
            console.error('Health check failed:', error.message);
            return false;
        }
    }

    /**
     * Get server statistics
     */
    async getStats() {
        try {
            const response = await this.axiosInstance.get('/stats');
            return response.data;
        } catch (error) {
            console.error('Failed to get stats:', error.message);
            return {};
        }
    }

    /**
     * Complete text using the vLLM server
     */
    async complete(request) {
        try {
            const response = await this.axiosInstance.post('/v1/completions', request);
            return response.data;
        } catch (error) {
            console.error('Completion failed:', error.message);
            throw error;
        }
    }

    /**
     * Stream text completion
     */
    async streamComplete(request, onChunk) {
        const streamRequest = { ...request, stream: true };
        
        try {
            const response = await this.axiosInstance.post('/v1/completions', streamRequest, {
                responseType: 'stream',
                timeout: 120000
            });

            return new Promise((resolve, reject) => {
                let fullText = '';

                response.data.on('data', (chunk) => {
                    const lines = chunk.toString().split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data === '[DONE]') {
                                resolve(fullText);
                                return;
                            }
                            
                            try {
                                const parsed = JSON.parse(data);
                                const text = parsed.text || '';
                                fullText += text;
                                if (onChunk) onChunk(text);
                            } catch (e) {
                                // Ignore JSON parse errors for incomplete chunks
                            }
                        }
                    }
                });

                response.data.on('error', reject);
                response.data.on('end', () => resolve(fullText));
            });
        } catch (error) {
            console.error('Stream completion failed:', error.message);
            throw error;
        }
    }
}

class VLLMExamples {
    constructor(client) {
        this.client = client;
    }

    /**
     * Basic text completion example
     */
    async basicCompletionExample() {
        console.log('=== Basic Completion Example ===');
        
        const request = {
            prompt: 'Explain the concept of machine learning in simple terms:',
            max_tokens: 200,
            temperature: 0.7
        };

        const startTime = performance.now();
        const result = await this.client.complete(request);
        const endTime = performance.now();

        console.log(`Prompt: ${request.prompt}`);
        console.log(`Response: ${result.text}`);
        console.log(`Tokens: ${result.usage.total_tokens}`);
        console.log(`Time: ${((endTime - startTime) / 1000).toFixed(2)}s`);
        console.log();
    }

    /**
     * Streaming completion example
     */
    async streamingExample() {
        console.log('=== Streaming Example ===');
        
        const request = {
            prompt: 'Tell me a story about a robot learning to paint:',
            max_tokens: 400,
            temperature: 0.8
        };

        console.log(`Prompt: ${request.prompt}`);
        console.log('Response (streaming): ');

        const startTime = performance.now();
        
        await this.client.streamComplete(request, (chunk) => {
            process.stdout.write(chunk);
        });
        
        const endTime = performance.now();
        console.log(`\nTime: ${((endTime - startTime) / 1000).toFixed(2)}s`);
        console.log();
    }

    /**
     * Batch processing example
     */
    async batchProcessingExample() {
        console.log('=== Batch Processing Example ===');
        
        const prompts = [
            'What is artificial intelligence?',
            'Explain quantum computing briefly.',
            'How does blockchain work?',
            'What is the future of renewable energy?'
        ];

        const requests = prompts.map(prompt => ({
            prompt,
            max_tokens: 150,
            temperature: 0.5
        }));

        const startTime = performance.now();
        
        // Process batch concurrently
        const results = await Promise.all(
            requests.map(req => this.client.complete(req))
        );
        
        const endTime = performance.now();

        results.forEach((result, i) => {
            console.log(`Question ${i + 1}: ${prompts[i]}`);
            console.log(`Answer: ${result.text.substring(0, 100)}...`);
            console.log(`Tokens: ${result.usage.total_tokens}`);
            console.log();
        });

        const totalTime = (endTime - startTime) / 1000;
        console.log(`Total batch time: ${totalTime.toFixed(2)}s`);
        console.log(`Average time per request: ${(totalTime / prompts.length).toFixed(2)}s`);
        console.log();
    }

    /**
     * Performance testing example
     */
    async performanceTest(numRequests = 5) {
        console.log(`=== Performance Test (${numRequests} requests) ===`);
        
        const request = {
            prompt: 'Count from 1 to 10:',
            max_tokens: 50,
            temperature: 0.1
        };

        const times = [];
        const tokenCounts = [];

        for (let i = 0; i < numRequests; i++) {
            const startTime = performance.now();
            const result = await this.client.complete(request);
            const endTime = performance.now();

            const time = (endTime - startTime) / 1000;
            times.push(time);
            tokenCounts.push(result.usage.total_tokens);

            console.log(`Request ${i + 1}: ${time.toFixed(2)}s, ${result.usage.total_tokens} tokens`);
        }

        const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
        const avgTokens = tokenCounts.reduce((a, b) => a + b, 0) / tokenCounts.length;
        const tokensPerSecond = avgTokens / avgTime;

        console.log('\nPerformance Summary:');
        console.log(`Average time: ${avgTime.toFixed(2)}s`);
        console.log(`Average tokens: ${avgTokens.toFixed(1)}`);
        console.log(`Tokens per second: ${tokensPerSecond.toFixed(1)}`);
        console.log();
    }

    /**
     * Integration example for car modification bot
     */
    async carModificationExample() {
        console.log('=== Car Modification Integration Example ===');
        
        const carDescription = 'Honda Civic 2018 sedan, silver color';
        const modificationRequest = 'sport body kit and black wheels';
        
        const prompt = `You are an expert car modification advisor. Given this car: "${carDescription}", 
provide detailed recommendations for: "${modificationRequest}".

Include:
1. Specific part recommendations
2. Estimated costs
3. Installation difficulty (1-10 scale)
4. Visual impact description

Car: ${carDescription}
Requested modifications: ${modificationRequest}

Recommendations:`;

        const request = {
            prompt,
            max_tokens: 500,
            temperature: 0.3,
            stop: ['\n\n\n']
        };

        console.log('Generating car modification recommendations...');
        
        const startTime = performance.now();
        const result = await this.client.complete(request);
        const endTime = performance.now();

        console.log(`\nCar: ${carDescription}`);
        console.log(`Modifications: ${modificationRequest}`);
        console.log(`\nRecommendations:\n${result.text}`);
        console.log(`\nGeneration time: ${((endTime - startTime) / 1000).toFixed(2)}s`);
        console.log(`Tokens used: ${result.usage.total_tokens}`);
        console.log();
    }
}

async function main() {
    const args = process.argv.slice(2);
    const serverUrl = args.includes('--server-url') 
        ? args[args.indexOf('--server-url') + 1] 
        : 'http://localhost:8000';
    
    const exampleType = args.includes('--example') 
        ? args[args.indexOf('--example') + 1] 
        : 'all';

    // Initialize client
    const client = new VLLMClient(serverUrl);

    // Check server health
    console.log('Checking server health...');
    const isHealthy = await client.healthCheck();
    
    if (!isHealthy) {
        console.log('❌ Server is not healthy. Please start the vLLM server first.');
        process.exit(1);
    }

    console.log('✅ Server is healthy');

    // Get server stats
    const stats = await client.getStats();
    if (Object.keys(stats).length > 0) {
        console.log(`Model: ${stats.model || 'Unknown'}`);
        console.log(`GPU: ${stats.gpu_name || 'Unknown'}`);
        console.log(`GPU Memory: ${(stats.gpu_memory_allocated || 0).toFixed(1)}GB / ${(stats.gpu_memory_total || 0).toFixed(1)}GB`);
        console.log();
    }

    // Run examples
    const examples = new VLLMExamples(client);

    try {
        if (exampleType === 'basic' || exampleType === 'all') {
            await examples.basicCompletionExample();
        }

        if (exampleType === 'stream' || exampleType === 'all') {
            await examples.streamingExample();
        }

        if (exampleType === 'batch' || exampleType === 'all') {
            await examples.batchProcessingExample();
        }

        if (exampleType === 'performance' || exampleType === 'all') {
            await examples.performanceTest();
        }

        if (exampleType === 'car' || exampleType === 'all') {
            await examples.carModificationExample();
        }

    } catch (error) {
        console.error('Example execution failed:', error.message);
        process.exit(1);
    }
}

// Handle command line execution
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { VLLMClient, VLLMExamples };
