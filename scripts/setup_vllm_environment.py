#!/usr/bin/env python3
"""
vLLM Environment Setup Script for NVIDIA GTX 1080 Ti

This script automates the setup of a Python environment optimized for running
vLLM on an NVIDIA GeForce GTX 1080 Ti with 11GB VRAM.

Usage:
    python setup_vllm_environment.py [--cuda-version 12.1] [--python-version 3.10]
"""

import os
import sys
import subprocess
import platform
import argparse
import json
from pathlib import Path
from typing import Dict, List, Optional


class VLLMEnvironmentSetup:
    def __init__(self, cuda_version: str = "12.1", python_version: str = "3.10"):
        self.cuda_version = cuda_version
        self.python_version = python_version
        self.system = platform.system().lower()
        self.env_name = "vllm-gtx1080ti"
        self.project_root = Path(__file__).parent.parent
        
    def run_command(self, command: List[str], check: bool = True) -> subprocess.CompletedProcess:
        """Run a command and return the result."""
        print(f"Running: {' '.join(command)}")
        try:
            result = subprocess.run(command, check=check, capture_output=True, text=True)
            if result.stdout:
                print(result.stdout)
            return result
        except subprocess.CalledProcessError as e:
            print(f"Error running command: {e}")
            if e.stderr:
                print(f"Error output: {e.stderr}")
            if check:
                raise
            return e
    
    def check_nvidia_driver(self) -> bool:
        """Check if NVIDIA driver is installed and working."""
        print("Checking NVIDIA driver...")
        try:
            result = self.run_command(["nvidia-smi"], check=False)
            if result.returncode == 0:
                print("✅ NVIDIA driver is working")
                return True
            else:
                print("❌ NVIDIA driver not found or not working")
                return False
        except FileNotFoundError:
            print("❌ nvidia-smi not found. Please install NVIDIA drivers.")
            return False
    
    def check_cuda(self) -> bool:
        """Check if CUDA is installed."""
        print("Checking CUDA installation...")
        try:
            result = self.run_command(["nvcc", "--version"], check=False)
            if result.returncode == 0:
                print("✅ CUDA is installed")
                return True
            else:
                print("❌ CUDA not found")
                return False
        except FileNotFoundError:
            print("❌ nvcc not found. Please install CUDA toolkit.")
            return False
    
    def check_conda(self) -> bool:
        """Check if conda is available."""
        try:
            result = self.run_command(["conda", "--version"], check=False)
            return result.returncode == 0
        except FileNotFoundError:
            return False
    
    def create_conda_environment(self) -> bool:
        """Create conda environment."""
        print(f"Creating conda environment: {self.env_name}")
        
        # Remove existing environment if it exists
        self.run_command(["conda", "env", "remove", "-n", self.env_name, "-y"], check=False)
        
        # Create new environment
        result = self.run_command([
            "conda", "create", "-n", self.env_name, 
            f"python={self.python_version}", "-y"
        ])
        
        return result.returncode == 0
    
    def create_venv_environment(self) -> bool:
        """Create virtual environment using venv."""
        print(f"Creating virtual environment: {self.env_name}")
        
        env_path = self.project_root / self.env_name
        
        # Remove existing environment if it exists
        if env_path.exists():
            import shutil
            shutil.rmtree(env_path)
        
        # Create new environment
        result = self.run_command([sys.executable, "-m", "venv", str(env_path)])
        
        return result.returncode == 0
    
    def get_pip_command(self) -> List[str]:
        """Get the appropriate pip command for the environment."""
        if self.check_conda():
            return ["conda", "run", "-n", self.env_name, "pip"]
        else:
            if self.system == "windows":
                return [str(self.project_root / self.env_name / "Scripts" / "pip.exe")]
            else:
                return [str(self.project_root / self.env_name / "bin" / "pip")]
    
    def install_pytorch(self) -> bool:
        """Install PyTorch with CUDA support."""
        print("Installing PyTorch with CUDA support...")
        
        pip_cmd = self.get_pip_command()
        
        # Determine PyTorch index URL based on CUDA version
        if self.cuda_version.startswith("12.1"):
            index_url = "https://download.pytorch.org/whl/cu121"
        elif self.cuda_version.startswith("11.8"):
            index_url = "https://download.pytorch.org/whl/cu118"
        else:
            print(f"Warning: Unsupported CUDA version {self.cuda_version}, using cu121")
            index_url = "https://download.pytorch.org/whl/cu121"
        
        # Install PyTorch
        result = self.run_command(pip_cmd + [
            "install", "torch", "torchvision", "torchaudio", 
            "--index-url", index_url
        ])
        
        return result.returncode == 0
    
    def install_vllm(self) -> bool:
        """Install vLLM and related dependencies."""
        print("Installing vLLM...")
        
        pip_cmd = self.get_pip_command()
        
        # Upgrade pip first
        self.run_command(pip_cmd + ["install", "--upgrade", "pip"])
        
        # Install vLLM
        result = self.run_command(pip_cmd + ["install", "vllm"])
        
        if result.returncode != 0:
            print("Failed to install vLLM, trying alternative installation...")
            # Try installing with no build isolation
            result = self.run_command(pip_cmd + [
                "install", "vllm", "--no-build-isolation"
            ])
        
        return result.returncode == 0
    
    def install_additional_packages(self) -> bool:
        """Install additional useful packages."""
        print("Installing additional packages...")
        
        pip_cmd = self.get_pip_command()
        
        packages = [
            "transformers",
            "accelerate",
            "huggingface_hub",
            "datasets",
            "tokenizers",
            "fastapi",
            "uvicorn",
            "requests",
            "numpy",
            "psutil",
            "gpustat"
        ]
        
        for package in packages:
            print(f"Installing {package}...")
            result = self.run_command(pip_cmd + ["install", package], check=False)
            if result.returncode != 0:
                print(f"Warning: Failed to install {package}")
        
        return True
    
    def verify_installation(self) -> bool:
        """Verify that the installation is working."""
        print("Verifying installation...")
        
        if self.check_conda():
            python_cmd = ["conda", "run", "-n", self.env_name, "python"]
        else:
            if self.system == "windows":
                python_cmd = [str(self.project_root / self.env_name / "Scripts" / "python.exe")]
            else:
                python_cmd = [str(self.project_root / self.env_name / "bin" / "python")]
        
        # Test PyTorch CUDA
        test_script = """
import torch
import sys
print(f"Python version: {sys.version}")
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA version: {torch.version.cuda}")
    print(f"GPU: {torch.cuda.get_device_name(0)}")
    print(f"GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
"""
        
        result = self.run_command(python_cmd + ["-c", test_script])
        
        if result.returncode != 0:
            print("❌ PyTorch CUDA verification failed")
            return False
        
        # Test vLLM import
        test_vllm = "import vllm; print(f'vLLM version: {vllm.__version__}')"
        result = self.run_command(python_cmd + ["-c", test_vllm])
        
        if result.returncode != 0:
            print("❌ vLLM import failed")
            return False
        
        print("✅ Installation verification successful!")
        return True
    
    def create_activation_script(self):
        """Create activation script for the environment."""
        if self.check_conda():
            script_content = f"""#!/bin/bash
# Activate vLLM environment for GTX 1080 Ti
conda activate {self.env_name}

# Set environment variables for optimal performance
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
export VLLM_USE_MODELSCOPE=False
export VLLM_WORKER_MULTIPROC_METHOD=spawn

echo "vLLM environment activated for GTX 1080 Ti"
echo "GPU Memory: $(nvidia-smi --query-gpu=memory.total --format=csv,noheader,nounits) MB"
"""
        else:
            if self.system == "windows":
                activate_path = self.project_root / self.env_name / "Scripts" / "activate.bat"
            else:
                activate_path = self.project_root / self.env_name / "bin" / "activate"
            
            script_content = f"""#!/bin/bash
# Activate vLLM environment for GTX 1080 Ti
source {activate_path}

# Set environment variables for optimal performance
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
export VLLM_USE_MODELSCOPE=False
export VLLM_WORKER_MULTIPROC_METHOD=spawn

echo "vLLM environment activated for GTX 1080 Ti"
echo "GPU Memory: $(nvidia-smi --query-gpu=memory.total --format=csv,noheader,nounits) MB"
"""
        
        script_path = self.project_root / "activate_vllm.sh"
        with open(script_path, "w") as f:
            f.write(script_content)
        
        # Make executable on Unix systems
        if self.system != "windows":
            os.chmod(script_path, 0o755)
        
        print(f"Created activation script: {script_path}")
    
    def setup(self) -> bool:
        """Run the complete setup process."""
        print("🚀 Starting vLLM environment setup for GTX 1080 Ti...")
        
        # Check prerequisites
        if not self.check_nvidia_driver():
            print("Please install NVIDIA drivers first.")
            return False
        
        if not self.check_cuda():
            print("Please install CUDA toolkit first.")
            return False
        
        # Create environment
        if self.check_conda():
            print("Using conda for environment management...")
            if not self.create_conda_environment():
                return False
        else:
            print("Using venv for environment management...")
            if not self.create_venv_environment():
                return False
        
        # Install packages
        if not self.install_pytorch():
            return False
        
        if not self.install_vllm():
            return False
        
        if not self.install_additional_packages():
            return False
        
        # Verify installation
        if not self.verify_installation():
            return False
        
        # Create activation script
        self.create_activation_script()
        
        print("🎉 vLLM environment setup completed successfully!")
        print(f"Environment name: {self.env_name}")
        print("To activate the environment, run:")
        if self.check_conda():
            print(f"  conda activate {self.env_name}")
        else:
            print(f"  source activate_vllm.sh")
        
        return True


def main():
    parser = argparse.ArgumentParser(description="Setup vLLM environment for GTX 1080 Ti")
    parser.add_argument("--cuda-version", default="12.1", help="CUDA version (default: 12.1)")
    parser.add_argument("--python-version", default="3.10", help="Python version (default: 3.10)")
    
    args = parser.parse_args()
    
    setup = VLLMEnvironmentSetup(args.cuda_version, args.python_version)
    
    if setup.setup():
        sys.exit(0)
    else:
        print("❌ Setup failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
