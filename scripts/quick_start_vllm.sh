#!/bin/bash
# Quick Start Script for vLLM on GTX 1080 Ti
# This script automates the entire setup process

set -e  # Exit on any error

echo "🚀 vLLM Quick Start for GTX 1080 Ti"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
MODEL_NAME="mistralai/Mistral-7B-Instruct-v0.1"
MODEL_SIZE="7b"
PORT=8000
SKIP_SETUP=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --model)
            MODEL_NAME="$2"
            shift 2
            ;;
        --model-size)
            MODEL_SIZE="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --skip-setup)
            SKIP_SETUP=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --model MODEL_NAME     Model to use (default: mistralai/Mistral-7B-Instruct-v0.1)"
            echo "  --model-size SIZE      Model size category: 3b, 7b, 13b_quantized (default: 7b)"
            echo "  --port PORT           Server port (default: 8000)"
            echo "  --skip-setup          Skip environment setup"
            echo "  --help                Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if running on Linux or Windows
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    PLATFORM="windows"
else
    PLATFORM="linux"
fi

print_status "Detected platform: $PLATFORM"

# Step 1: Check prerequisites
print_step "Checking prerequisites..."

# Check NVIDIA driver
if command -v nvidia-smi &> /dev/null; then
    print_status "✅ NVIDIA driver found"
    GPU_INFO=$(nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits)
    print_status "GPU: $GPU_INFO"
else
    print_error "❌ NVIDIA driver not found. Please install NVIDIA drivers first."
    exit 1
fi

# Check CUDA
if command -v nvcc &> /dev/null; then
    CUDA_VERSION=$(nvcc --version | grep "release" | sed 's/.*release \([0-9]\+\.[0-9]\+\).*/\1/')
    print_status "✅ CUDA $CUDA_VERSION found"
else
    print_warning "⚠️ CUDA toolkit not found. Will attempt to install PyTorch with CUDA."
fi

# Check Python
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
    print_status "✅ Python $PYTHON_VERSION found"
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_VERSION=$(python --version | cut -d' ' -f2)
    print_status "✅ Python $PYTHON_VERSION found"
    PYTHON_CMD="python"
else
    print_error "❌ Python not found. Please install Python 3.8+ first."
    exit 1
fi

# Step 2: Setup environment (if not skipped)
if [ "$SKIP_SETUP" = false ]; then
    print_step "Setting up Python environment..."
    
    # Run the setup script
    if [ -f "scripts/setup_vllm_environment.py" ]; then
        $PYTHON_CMD scripts/setup_vllm_environment.py --cuda-version 12.1 --python-version 3.10
        if [ $? -ne 0 ]; then
            print_error "Environment setup failed. Please check the logs above."
            exit 1
        fi
    else
        print_error "Setup script not found. Please run from the project root directory."
        exit 1
    fi
else
    print_status "Skipping environment setup as requested."
fi

# Step 3: Activate environment
print_step "Activating vLLM environment..."

# Check if conda is available
if command -v conda &> /dev/null; then
    print_status "Using conda environment"
    ENV_ACTIVATION="conda activate vllm-gtx1080ti"
    PYTHON_CMD="conda run -n vllm-gtx1080ti python"
else
    print_status "Using virtual environment"
    if [ "$PLATFORM" = "windows" ]; then
        ENV_ACTIVATION="source vllm-gtx1080ti/Scripts/activate"
        PYTHON_CMD="vllm-gtx1080ti/Scripts/python.exe"
    else
        ENV_ACTIVATION="source vllm-gtx1080ti/bin/activate"
        PYTHON_CMD="vllm-gtx1080ti/bin/python"
    fi
fi

# Step 4: Verify installation
print_step "Verifying vLLM installation..."

$PYTHON_CMD -c "
import torch
import vllm
print(f'✅ PyTorch {torch.__version__} with CUDA {torch.version.cuda}')
print(f'✅ vLLM {vllm.__version__}')
print(f'✅ CUDA available: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'✅ GPU: {torch.cuda.get_device_name(0)}')
    print(f'✅ GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB')
" 2>/dev/null

if [ $? -ne 0 ]; then
    print_error "❌ vLLM verification failed. Please check the installation."
    exit 1
fi

# Step 5: Download model (if needed)
print_step "Checking model availability..."

MODEL_CACHE_DIR="$HOME/.cache/huggingface/transformers"
if [ ! -d "$MODEL_CACHE_DIR" ]; then
    mkdir -p "$MODEL_CACHE_DIR"
fi

print_status "Model will be downloaded automatically on first use: $MODEL_NAME"

# Step 6: Start the server
print_step "Starting vLLM server..."

print_status "Configuration:"
print_status "  Model: $MODEL_NAME"
print_status "  Model Size: $MODEL_SIZE"
print_status "  Port: $PORT"
print_status "  Server URL: http://localhost:$PORT"

# Create a temporary script to run the server
SERVER_SCRIPT=$(mktemp)
cat > "$SERVER_SCRIPT" << EOF
#!/usr/bin/env python3
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from vllm_server import main
import sys

# Set command line arguments
sys.argv = [
    'vllm_server.py',
    '--model', '$MODEL_NAME',
    '--model-size', '$MODEL_SIZE',
    '--host', '0.0.0.0',
    '--port', '$PORT'
]

if __name__ == '__main__':
    main()
EOF

chmod +x "$SERVER_SCRIPT"

print_status "Starting server... (This may take a few minutes for model loading)"
print_status "Press Ctrl+C to stop the server"

# Set environment variables for optimal performance
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
export VLLM_USE_MODELSCOPE=False
export VLLM_WORKER_MULTIPROC_METHOD=spawn

# Start the server
$PYTHON_CMD "$SERVER_SCRIPT"

# Cleanup
rm -f "$SERVER_SCRIPT"
