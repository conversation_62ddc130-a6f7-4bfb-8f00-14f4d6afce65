#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { apiTestUtils } from '../src/utils/api-test';
import { logger } from '../src/utils/logger';

// Load environment variables
dotenv.config();

/**
 * Script to test API connections and functionality
 */
async function main() {
  console.log('🚗 PimpMyRideAI API Test Script');
  console.log('================================\n');

  try {
    // Test basic connections first
    console.log('📡 Testing API connections...\n');
    const connectionResults = await apiTestUtils.testAllConnections();

    if (!connectionResults.overall) {
      console.error('❌ API connection tests failed!');
      console.error('Please check your API keys and network connection.');
      process.exit(1);
    }

    console.log('✅ All API connections successful!\n');

    // Test car recognition with a sample image (if provided)
    const testImageUrl = process.argv[2];
    if (testImageUrl) {
      console.log('🔍 Testing car recognition...\n');
      const carRecognitionResult = await apiTestUtils.testCarRecognition(testImageUrl);
      
      if (carRecognitionResult) {
        console.log('✅ Car recognition test successful!\n');
      } else {
        console.log('❌ Car recognition test failed!\n');
      }
    }

    // Test image generation
    console.log('🎨 Testing image generation...\n');
    const imageGenerationResult = await apiTestUtils.testImageGeneration();
    
    if (imageGenerationResult) {
      console.log('✅ Image generation test successful!\n');
    } else {
      console.log('❌ Image generation test failed!\n');
    }

    console.log('🎉 API testing completed!');
    console.log('\nNext steps:');
    console.log('1. Start the bot with: npm run dev');
    console.log('2. Send /start to your Telegram bot');
    console.log('3. Upload a car image to test the full workflow');

  } catch (error) {
    console.error('💥 Test script failed:', error);
    logger.error('Test script error:', error);
    process.exit(1);
  }
}

// Handle script arguments
if (require.main === module) {
  if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log('Usage: npm run test:apis [image-url]');
    console.log('');
    console.log('Options:');
    console.log('  image-url    Optional URL of a car image to test recognition');
    console.log('  --help, -h   Show this help message');
    console.log('');
    console.log('Examples:');
    console.log('  npm run test:apis');
    console.log('  npm run test:apis https://example.com/car.jpg');
    process.exit(0);
  }

  main().catch((error) => {
    console.error('Script execution failed:', error);
    process.exit(1);
  });
}
