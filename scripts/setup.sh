#!/bin/bash

# Car Modification Generator Setup Script

set -e

echo "🚗 Setting up Car Modification Generator..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed"
    exit 1
fi

echo "✅ npm $(npm -v) detected"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your API keys and configuration"
else
    echo "✅ .env file already exists"
fi

# Check if Docker is available for database setup
if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
    echo "🐳 Docker detected. Setting up database..."
    
    # Start PostgreSQL container
    docker-compose up -d postgres
    
    # Wait for database to be ready
    echo "⏳ Waiting for database to be ready..."
    sleep 10
    
    # Run database migrations
    echo "🗄️  Running database migrations..."
    npm run db:push
    
    echo "✅ Database setup complete"
else
    echo "⚠️  Docker not found. Please set up PostgreSQL manually and update DATABASE_URL in .env"
fi

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npm run db:generate

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p uploads
mkdir -p logs

# Build the project
echo "🔨 Building project..."
npm run build

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit .env file with your API keys:"
echo "   - TELEGRAM_BOT_TOKEN (get from @BotFather)"
echo "   - OPENAI_API_KEY (get from OpenAI)"
echo "   - DATABASE_URL (if not using Docker)"
echo ""
echo "2. Start the development server:"
echo "   npm run dev"
echo ""
echo "3. Or start with Docker:"
echo "   docker-compose up"
echo ""
echo "4. Test your bot by sending /start to your Telegram bot"
echo ""
echo "📚 Check README.md for detailed documentation"
