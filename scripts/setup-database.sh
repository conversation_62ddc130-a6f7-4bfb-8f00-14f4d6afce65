#!/bin/bash

# PimpMyRideAI Database Setup Script
# This script automates the database setup process

set -e  # Exit on any error

echo "🗄️ PimpMyRideAI Database Setup"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if PostgreSQL is installed
check_postgresql() {
    print_step "Checking PostgreSQL installation..."
    
    if command -v psql &> /dev/null; then
        print_status "PostgreSQL is installed"
        psql --version
    else
        print_error "PostgreSQL is not installed!"
        echo "Please install PostgreSQL first:"
        echo "  Ubuntu/Debian: sudo apt install postgresql postgresql-contrib"
        echo "  macOS: brew install postgresql@14"
        echo "  Windows: Download from https://www.postgresql.org/download/windows/"
        exit 1
    fi
}

# Check if PostgreSQL service is running
check_postgresql_service() {
    print_step "Checking PostgreSQL service..."
    
    if systemctl is-active --quiet postgresql 2>/dev/null; then
        print_status "PostgreSQL service is running"
    elif brew services list | grep postgresql | grep started &> /dev/null; then
        print_status "PostgreSQL service is running (macOS)"
    else
        print_warning "PostgreSQL service might not be running"
        echo "Try starting it with:"
        echo "  Linux: sudo systemctl start postgresql"
        echo "  macOS: brew services start postgresql@14"
    fi
}

# Get database credentials
get_credentials() {
    print_step "Database configuration..."
    
    # Default values
    DB_NAME="car_modifications"
    DB_USER="pimpmyride"
    
    echo "Enter database configuration (press Enter for defaults):"
    
    read -p "Database name [$DB_NAME]: " input_db_name
    DB_NAME=${input_db_name:-$DB_NAME}
    
    read -p "Database user [$DB_USER]: " input_db_user
    DB_USER=${input_db_user:-$DB_USER}
    
    read -s -p "Database password: " DB_PASSWORD
    echo
    
    if [ -z "$DB_PASSWORD" ]; then
        print_error "Password cannot be empty!"
        exit 1
    fi
    
    read -p "PostgreSQL admin user [postgres]: " ADMIN_USER
    ADMIN_USER=${ADMIN_USER:-postgres}
    
    print_status "Configuration:"
    echo "  Database: $DB_NAME"
    echo "  User: $DB_USER"
    echo "  Admin: $ADMIN_USER"
}

# Create database and user
create_database() {
    print_step "Creating database and user..."
    
    # Create SQL commands
    SQL_COMMANDS="
    -- Create database
    CREATE DATABASE $DB_NAME;
    
    -- Create user
    CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';
    
    -- Grant privileges
    GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;
    
    -- Connect to the new database
    \\c $DB_NAME;
    
    -- Grant schema privileges
    GRANT ALL ON SCHEMA public TO $DB_USER;
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $DB_USER;
    GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $DB_USER;
    
    -- Set default privileges for future tables
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO $DB_USER;
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO $DB_USER;
    "
    
    # Execute SQL commands
    echo "$SQL_COMMANDS" | psql -U $ADMIN_USER -h localhost postgres
    
    if [ $? -eq 0 ]; then
        print_status "Database and user created successfully"
    else
        print_error "Failed to create database and user"
        exit 1
    fi
}

# Update .env file
update_env_file() {
    print_step "Updating .env file..."
    
    DATABASE_URL="postgresql://$DB_USER:$DB_PASSWORD@localhost:5432/$DB_NAME"
    
    if [ -f .env ]; then
        # Update existing .env file
        if grep -q "DATABASE_URL=" .env; then
            # Replace existing DATABASE_URL
            if [[ "$OSTYPE" == "darwin"* ]]; then
                # macOS
                sed -i '' "s|DATABASE_URL=.*|DATABASE_URL=\"$DATABASE_URL\"|" .env
            else
                # Linux
                sed -i "s|DATABASE_URL=.*|DATABASE_URL=\"$DATABASE_URL\"|" .env
            fi
            print_status "Updated DATABASE_URL in .env file"
        else
            # Add DATABASE_URL to existing file
            echo "DATABASE_URL=\"$DATABASE_URL\"" >> .env
            print_status "Added DATABASE_URL to .env file"
        fi
    else
        # Create new .env file from example
        if [ -f .env.example ]; then
            cp .env.example .env
            if [[ "$OSTYPE" == "darwin"* ]]; then
                sed -i '' "s|DATABASE_URL=.*|DATABASE_URL=\"$DATABASE_URL\"|" .env
            else
                sed -i "s|DATABASE_URL=.*|DATABASE_URL=\"$DATABASE_URL\"|" .env
            fi
            print_status "Created .env file from .env.example"
        else
            # Create minimal .env file
            cat > .env << EOF
# Database Configuration
DATABASE_URL="$DATABASE_URL"

# Add other required environment variables here
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
PROXYAPI_KEY=your_proxyapi_key_here
OPENROUTER_API_KEY=your_openrouter_key_here
EOF
            print_status "Created new .env file"
        fi
    fi
}

# Test database connection
test_connection() {
    print_step "Testing database connection..."
    
    if npm run db:check &> /dev/null; then
        print_status "Database connection test successful"
    else
        print_warning "Database connection test failed"
        echo "You may need to:"
        echo "1. Check if PostgreSQL is running"
        echo "2. Verify the credentials"
        echo "3. Run: npm run db:check for detailed error info"
    fi
}

# Setup Prisma
setup_prisma() {
    print_step "Setting up Prisma..."
    
    # Generate Prisma client
    print_status "Generating Prisma client..."
    npm run db:generate
    
    # Apply database schema
    print_status "Applying database schema..."
    npm run db:push
    
    # Seed database with initial data
    print_status "Seeding database with initial data..."
    npm run db:seed
    
    print_status "Prisma setup completed"
}

# Main execution
main() {
    echo "Starting database setup process..."
    echo
    
    # Check prerequisites
    check_postgresql
    check_postgresql_service
    
    # Get configuration
    get_credentials
    echo
    
    # Create database
    create_database
    echo
    
    # Update environment file
    update_env_file
    echo
    
    # Setup Prisma
    setup_prisma
    echo
    
    # Test connection
    test_connection
    echo
    
    print_status "Database setup completed successfully! 🎉"
    echo
    echo "Next steps:"
    echo "1. Update your .env file with API keys:"
    echo "   - TELEGRAM_BOT_TOKEN"
    echo "   - PROXYAPI_KEY"
    echo "   - OPENROUTER_API_KEY"
    echo
    echo "2. Start the bot:"
    echo "   npm run dev"
    echo
    echo "3. Open Prisma Studio to view data:"
    echo "   npm run db:studio"
}

# Run main function
main
