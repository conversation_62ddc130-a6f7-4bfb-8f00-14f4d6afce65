{"name": "car-modification-generator", "version": "1.0.0", "description": "Telegram bot for generating car modifications with AI visualization and cost estimation", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only -r tsconfig-paths/register src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:apis": "ts-node scripts/test-apis.ts", "test:bot": "ts-node scripts/test-bot-functionality.ts", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node scripts/seed-database.ts", "db:seed-parts": "ts-node scripts/seed-parts-database.ts", "db:check": "ts-node scripts/check-database.ts", "db:reset": "prisma migrate reset --force && npm run db:seed", "vllm:setup": "python scripts/setup_vllm_environment.py", "vllm:start": "bash scripts/quick_start_vllm.sh", "vllm:test": "node examples/vllm_client.js"}, "keywords": ["telegram", "bot", "car", "modification", "ai", "visualization", "proxyapi", "image-editing", "automotive"], "author": "Car Modification Generator Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "axios": "^1.6.2", "dotenv": "^16.3.1", "express": "^4.18.2", "form-data": "^4.0.3", "module-alias": "^2.2.3", "node-telegram-bot-api": "^0.64.0", "sharp": "^0.33.1", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/form-data": "^2.2.1", "@types/jest": "^29.5.8", "@types/node": "^20.10.0", "@types/node-telegram-bot-api": "^0.64.7", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "prisma": "^5.7.1", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}, "_moduleAliases": {"@": "./dist"}}