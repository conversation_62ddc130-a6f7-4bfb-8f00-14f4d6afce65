#!/usr/bin/env python3
"""
Простой скрипт для запуска vLLM сервера на GTX 1080
"""

import argparse
import subprocess
import sys
import torch

def check_gpu():
    """Проверка GPU"""
    if not torch.cuda.is_available():
        print("❌ CUDA недоступна!")
        return False
    
    gpu_name = torch.cuda.get_device_name(0)
    vram_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
    
    print(f"✅ GPU: {gpu_name}")
    print(f"✅ VRAM: {vram_gb:.1f} GB")
    
    return True

def start_server(model_name, port=8000, host="0.0.0.0"):
    """Запуск vLLM сервера"""
    
    if not check_gpu():
        sys.exit(1)
    
    print(f"\n🚀 Запуск vLLM сервера...")
    print(f"📦 Модель: {model_name}")
    print(f"🌐 Адрес: http://{host}:{port}")
    print(f"⚠️  Первый запуск может занять время для загрузки модели...")
    
    # Команда для запуска vLLM сервера
    cmd = [
        "python", "-m", "vllm.entrypoints.openai.api_server",
        "--model", model_name,
        "--host", host,
        "--port", str(port),
        "--gpu-memory-utilization", "0.85",  # 85% VRAM для GTX 1080
        "--dtype", "float16",  # Половинная точность
        "--max-model-len", "2048",  # Ограничение контекста
    ]
    
    # Дополнительные параметры для некоторых моделей
    if "phi-3" in model_name.lower():
        cmd.extend(["--trust-remote-code"])
    
    print(f"\n🔧 Команда: {' '.join(cmd)}")
    print("\n" + "="*50)
    
    try:
        # Запуск сервера
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n\n🛑 Сервер остановлен пользователем")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Ошибка запуска сервера: {e}")
        sys.exit(1)

def main():
    """Главная функция"""
    parser = argparse.ArgumentParser(description="Запуск vLLM сервера для GTX 1080")
    parser.add_argument(
        "--model", 
        default="microsoft/Phi-3-mini-4k-instruct",
        help="Название модели (по умолчанию: microsoft/Phi-3-mini-4k-instruct)"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        default=8000,
        help="Порт сервера (по умолчанию: 8000)"
    )
    parser.add_argument(
        "--host", 
        default="0.0.0.0",
        help="Хост сервера (по умолчанию: 0.0.0.0)"
    )
    
    args = parser.parse_args()
    
    print("🎯 vLLM Сервер для GTX 1080")
    print("=" * 30)
    
    # Предустановленные модели для GTX 1080
    recommended_models = {
        "1": ("microsoft/Phi-3-mini-4k-instruct", "Phi-3 Mini (4GB VRAM, быстрая)"),
        "2": ("TinyLlama/TinyLlama-1.1B-Chat-v1.0", "TinyLlama (1GB VRAM, очень быстрая)"),
        "3": ("microsoft/DialoGPT-small", "DialoGPT Small (117MB, для тестов)"),
        "4": ("custom", "Своя модель")
    }
    
    if len(sys.argv) == 1:  # Если нет аргументов командной строки
        print("\n🎮 Выберите модель:")
        for key, (model, desc) in recommended_models.items():
            print(f"{key}. {desc}")
        
        choice = input("\nВведите номер (1-4): ").strip()
        
        if choice in recommended_models:
            if choice == "4":
                model_name = input("Введите название модели: ").strip()
            else:
                model_name = recommended_models[choice][0]
        else:
            print("❌ Неверный выбор!")
            sys.exit(1)
    else:
        model_name = args.model
    
    start_server(model_name, args.port, args.host)

if __name__ == "__main__":
    main()
