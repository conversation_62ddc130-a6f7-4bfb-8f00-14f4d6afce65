// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id           Int      @id @default(autoincrement())
  telegramId   BigInt   @unique
  username     String?
  firstName    String
  lastName     String?
  languageCode String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  sessions     UserSession[]
  modifications ModificationHistory[]

  @@map("users")
}

model UserSession {
  id           String   @id @default(cuid())
  userId       Int
  chatId       BigInt
  currentStep  String
  uploadedImage String?
  carInfo      Json?
  lastActivity DateTime @default(now())
  createdAt    DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

model Car {
  id         String   @id @default(cuid())
  make       String
  model      String
  year       Int
  generation String?
  bodyType   String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  modifications ModificationHistory[]
  compatibleParts ModificationPart[]

  @@unique([make, model, year, generation])
  @@map("cars")
}

model ModificationCategory {
  id          String   @id @default(cuid())
  name        String   @unique
  description String
  createdAt   DateTime @default(now())

  // Relations
  parts ModificationPart[]

  @@map("modification_categories")
}

model ModificationPart {
  id                   String   @id @default(cuid())
  name                 String
  categoryId           String
  description          String
  averagePrice         Decimal  @db.Decimal(10, 2)
  priceRub             Decimal  @db.Decimal(10, 2) // Цена в рублях
  installationComplexity String
  installationTime     String   @default("2-4 часа") // Время установки
  brand                String?
  partNumber           String?
  imageUrl             String?

  // Информация о наличии и доставке
  availability         String   @default("В наличии") // "В наличии", "Под заказ", "Нет в наличии"
  deliveryDaysMin      Int      @default(1) // Минимальные дни доставки
  deliveryDaysMax      Int      @default(3) // Максимальные дни доставки

  // Гарантия и качество
  warrantyMonths       Int      @default(6) // Гарантия в месяцах
  qualityRating        Decimal? @db.Decimal(3, 2) // Рейтинг качества 1-5

  // Дополнительная информация
  weight               Decimal? @db.Decimal(8, 3) // Вес в кг
  material             String? // Материал изготовления
  origin               String   @default("Россия") // Страна происхождения

  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  // Relations
  category     ModificationCategory @relation(fields: [categoryId], references: [id])
  compatibleCars Car[]
  usedInModifications ModificationHistory[]

  @@map("modification_parts")
}

model ModificationHistory {
  id                String   @id @default(cuid())
  userId            Int
  carId             String
  originalImageUrl  String
  modifiedImageUrl  String
  userRequest       String
  appliedParts      String[] // Array of part IDs
  totalCost         Decimal  @db.Decimal(10, 2)
  laborCost         Decimal  @db.Decimal(10, 2)
  description       String
  installationNotes String
  createdAt         DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id])
  car  Car  @relation(fields: [carId], references: [id])
  parts ModificationPart[]

  @@map("modification_history")
}

model Supplier {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  website     String?
  phone       String?
  email       String?
  rating      Decimal? @db.Decimal(3, 2) // Рейтинг поставщика 1-5
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  prices PartPrice[]

  @@map("suppliers")
}

model PartPrice {
  id          String   @id @default(cuid())
  partId      String
  supplierId  String
  price       Decimal  @db.Decimal(10, 2)
  priceRub    Decimal  @db.Decimal(10, 2)
  currency    String   @default("RUB")
  availability String  @default("В наличии")
  deliveryDays Int     @default(3)
  isActive    Boolean  @default(true)
  validUntil  DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  supplier Supplier @relation(fields: [supplierId], references: [id])

  @@map("part_prices")
}

model PriceHistory {
  id        String   @id @default(cuid())
  partId    String
  price     Decimal  @db.Decimal(10, 2)
  supplier  String
  currency  String   @default("RUB")
  createdAt DateTime @default(now())

  @@map("price_history")
}

model ApiUsage {
  id        String   @id @default(cuid())
  userId    Int?
  service   String   // 'openai', 'telegram', etc.
  operation String   // 'image_generation', 'car_recognition', etc.
  cost      Decimal? @db.Decimal(8, 4)
  tokens    Int?
  success   Boolean  @default(true)
  createdAt DateTime @default(now())

  @@map("api_usage")
}
