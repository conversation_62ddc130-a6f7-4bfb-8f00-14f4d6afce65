#!/usr/bin/env python3
"""
Простой скрипт для тестирования генеративной модели на GTX 1080
"""

import sys
import time
import torch
import subprocess

def check_environment():
    """Проверка окружения"""
    print("🔍 Проверка окружения...")
    
    # Проверка PyTorch
    try:
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"✅ CUDA доступна: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✅ GPU: {torch.cuda.get_device_name(0)}")
            print(f"✅ VRAM: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        else:
            print("❌ CUDA недоступна!")
            return False
    except Exception as e:
        print(f"❌ Ошибка PyTorch: {e}")
        return False
    
    # Проверка vLLM
    try:
        import vllm
        print(f"✅ vLLM: {vllm.__version__}")
    except ImportError:
        print("❌ vLLM не установлен!")
        return False
    
    return True

def test_simple_model():
    """Тест простой модели"""
    print("\n🚀 Запуск простой модели...")
    
    try:
        from vllm import LLM, SamplingParams
        
        # Используем очень маленькую модель для теста
        model_name = "microsoft/DialoGPT-small"  # ~117MB
        
        print(f"📥 Загрузка модели: {model_name}")
        
        # Настройки для GTX 1080 (8GB VRAM)
        llm = LLM(
            model=model_name,
            gpu_memory_utilization=0.8,  # 80% от 8GB = 6.4GB
            max_model_len=512,  # Короткий контекст для экономии памяти
            dtype="float16",  # Половинная точность
        )
        
        # Параметры генерации
        sampling_params = SamplingParams(
            temperature=0.7,
            top_p=0.9,
            max_tokens=100
        )
        
        # Тестовые промпты
        prompts = [
            "Привет! Как дела?",
            "Расскажи о машинном обучении",
            "Что такое искусственный интеллект?"
        ]
        
        print("💬 Генерация ответов...")
        start_time = time.time()
        
        outputs = llm.generate(prompts, sampling_params)
        
        end_time = time.time()
        
        print(f"\n⏱️ Время генерации: {end_time - start_time:.2f} секунд")
        print(f"🔥 Скорость: {len(outputs) / (end_time - start_time):.2f} ответов/сек")
        
        # Показать результаты
        for i, output in enumerate(outputs):
            prompt = output.prompt
            generated_text = output.outputs[0].text
            print(f"\n📝 Промпт {i+1}: {prompt}")
            print(f"🤖 Ответ: {generated_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании модели: {e}")
        return False

def test_phi3_mini():
    """Тест Phi-3 Mini (рекомендуемая модель для GTX 1080)"""
    print("\n🚀 Запуск Phi-3 Mini...")
    
    try:
        from vllm import LLM, SamplingParams
        
        model_name = "microsoft/Phi-3-mini-4k-instruct"
        
        print(f"📥 Загрузка модели: {model_name}")
        print("⚠️  Это может занять несколько минут при первом запуске...")
        
        # Настройки для GTX 1080
        llm = LLM(
            model=model_name,
            gpu_memory_utilization=0.85,  # 85% от 8GB
            max_model_len=2048,  # Средний контекст
            dtype="float16",
            trust_remote_code=True
        )
        
        sampling_params = SamplingParams(
            temperature=0.7,
            top_p=0.9,
            max_tokens=200
        )
        
        prompts = [
            "Объясни простыми словами, что такое нейронные сети",
            "Напиши короткий Python код для сортировки списка",
            "Какие преимущества у электромобилей?"
        ]
        
        print("💬 Генерация ответов...")
        start_time = time.time()
        
        outputs = llm.generate(prompts, sampling_params)
        
        end_time = time.time()
        
        print(f"\n⏱️ Время генерации: {end_time - start_time:.2f} секунд")
        print(f"🔥 Скорость: {len(outputs) / (end_time - start_time):.2f} ответов/сек")
        
        for i, output in enumerate(outputs):
            prompt = output.prompt
            generated_text = output.outputs[0].text
            print(f"\n📝 Промпт {i+1}: {prompt}")
            print(f"🤖 Ответ: {generated_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании Phi-3 Mini: {e}")
        return False

def main():
    """Главная функция"""
    print("🎯 Тестирование генеративных моделей на GTX 1080")
    print("=" * 50)
    
    # Проверка окружения
    if not check_environment():
        print("\n❌ Окружение не готово. Проверьте установку PyTorch и vLLM.")
        sys.exit(1)
    
    print("\n🎮 Выберите тест:")
    print("1. Простая модель (DialoGPT-small)")
    print("2. Phi-3 Mini (рекомендуется)")
    print("3. Оба теста")
    
    choice = input("\nВведите номер (1-3): ").strip()
    
    if choice == "1":
        test_simple_model()
    elif choice == "2":
        test_phi3_mini()
    elif choice == "3":
        test_simple_model()
        test_phi3_mini()
    else:
        print("❌ Неверный выбор!")
        sys.exit(1)
    
    print("\n🎉 Тестирование завершено!")

if __name__ == "__main__":
    main()
