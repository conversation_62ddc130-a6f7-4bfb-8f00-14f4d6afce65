# vLLM Setup for NVIDIA GTX 1080 Ti

This guide provides everything you need to deploy generative AI models locally on your GTX 1080 Ti using vLLM.

## 🚀 Quick Start

### Prerequisites
- NVIDIA GeForce GTX 1080 Ti (11GB VRAM)
- NVIDIA Driver 470.57.02+ (latest recommended)
- Python 3.8-3.11 (3.10 recommended)
- 16GB+ system RAM
- 50GB+ free storage

### One-Command Setup
```bash
# Clone and setup everything
git clone <your-repo>
cd PimpMyRideAI
chmod +x scripts/quick_start_vllm.sh
./scripts/quick_start_vllm.sh
```

### Manual Setup
```bash
# 1. Setup Python environment
python scripts/setup_vllm_environment.py

# 2. Start vLLM server
python src/vllm_server.py --model mistralai/Mistral-7B-Instruct-v0.1

# 3. Test the server
python examples/vllm_client.py
```

## 📋 What's Included

### Core Files
- **`docs/VLLM_SETUP_GUIDE.md`** - Comprehensive setup instructions
- **`scripts/setup_vllm_environment.py`** - Automated environment setup
- **`src/vllm_server.py`** - Optimized vLLM server for GTX 1080 Ti
- **`examples/vllm_client.py`** - Python client examples
- **`examples/vllm_client.js`** - Node.js client examples

### Documentation
- **`docs/VLLM_PERFORMANCE_GUIDE.md`** - Performance optimization
- **`docs/VLLM_TROUBLESHOOTING.md`** - Common issues and solutions

### Scripts
- **`scripts/quick_start_vllm.sh`** - One-command setup and start
- **`scripts/setup_vllm_environment.py`** - Environment configuration

## 🎯 Recommended Models

### For GTX 1080 (8GB VRAM) - Updated for Your Hardware

#### Small Models (Recommended for 8GB VRAM)
```bash
# Phi-3 Mini - 4GB VRAM, very fast, best for 8GB cards
python src/vllm_server.py --model microsoft/Phi-3-mini-4k-instruct --model-size 3b

# Mistral 7B AWQ - 4GB VRAM, quantized for efficiency
python src/vllm_server.py --model TheBloke/Mistral-7B-Instruct-v0.1-AWQ --model-size 7b

# Llama 2 7B GPTQ - 4-5GB VRAM, high quality
python src/vllm_server.py --model TheBloke/Llama-2-7B-Chat-GPTQ --model-size 7b
```

#### Standard Models (Use with caution on 8GB)
```bash
# Mistral 7B - 7GB VRAM, may be tight on 8GB cards
python src/vllm_server.py --model mistralai/Mistral-7B-Instruct-v0.1 --model-size 7b

# CodeLlama 7B - 7GB VRAM, for coding tasks
python src/vllm_server.py --model codellama/CodeLlama-7b-Instruct-hf --model-size 7b
```

#### Tiny Models (Ultra-fast)
```bash
# TinyLlama - 1GB VRAM, extremely fast
python src/vllm_server.py --model TinyLlama/TinyLlama-1.1B-Chat-v1.0 --model-size 3b
```

## 🔧 Configuration Examples

### Memory Optimized (Conservative)
```python
# For stable 24/7 operation
engine_args = AsyncEngineArgs(
    model="mistralai/Mistral-7B-Instruct-v0.1",
    gpu_memory_utilization=0.75,
    max_model_len=2048,
    max_num_seqs=8,
    swap_space=4
)
```

### Performance Optimized (Aggressive)
```python
# For maximum throughput
engine_args = AsyncEngineArgs(
    model="microsoft/Phi-3-mini-4k-instruct",
    gpu_memory_utilization=0.9,
    max_model_len=4096,
    max_num_seqs=32,
    max_num_batched_tokens=4096
)
```

## 📊 Expected Performance on GTX 1080 (8GB VRAM)

| Model | VRAM Usage | Tokens/Second | Batch Size | Use Case |
|-------|------------|---------------|------------|----------|
| TinyLlama 1.1B | 1GB | 80-120 | 64 | Ultra-fast responses |
| Phi-3 Mini | 4GB | 35-50 | 32 | Fast responses |
| Mistral 7B AWQ | 4GB | 20-30 | 24 | Memory efficient |
| Llama 2 7B GPTQ | 5GB | 15-25 | 16 | High quality |
| Mistral 7B | 7GB | 12-20 | 12 | Balanced (tight fit) |

## 🛠 Usage Examples

### Python Client
```python
from examples.vllm_client import VLLMClient, CompletionRequest

async with VLLMClient("http://localhost:8000") as client:
    request = CompletionRequest(
        prompt="Explain machine learning:",
        max_tokens=200,
        temperature=0.7
    )
    result = await client.complete_async(request)
    print(result['text'])
```

### Node.js Client
```javascript
const { VLLMClient } = require('./examples/vllm_client.js');

const client = new VLLMClient('http://localhost:8000');
const result = await client.complete({
    prompt: "Write a Python function:",
    max_tokens: 300,
    temperature: 0.3
});
console.log(result.text);
```

### cURL
```bash
curl -X POST "http://localhost:8000/v1/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "What is artificial intelligence?",
    "max_tokens": 150,
    "temperature": 0.7
  }'
```

## 🔍 Monitoring

### Check Server Status
```bash
# Health check
curl http://localhost:8000/health

# Server statistics
curl http://localhost:8000/stats

# GPU monitoring
nvidia-smi -l 1
```

### Performance Testing
```bash
# Python performance test
python examples/vllm_client.py --example performance

# Node.js performance test  
node examples/vllm_client.js --example performance
```

## 🚨 Troubleshooting

### Common Issues

#### Out of Memory
```bash
# Reduce memory usage
python src/vllm_server.py --model microsoft/Phi-3-mini-4k-instruct --model-size 3b
```

#### Slow Performance
```bash
# Check GPU clocks
nvidia-smi -q -d clock

# Set performance mode
sudo nvidia-smi -pm 1
sudo nvidia-smi -ac 5505,1911
```

#### Installation Issues
```bash
# Reinstall with CUDA support
pip uninstall torch vllm
pip install torch --index-url https://download.pytorch.org/whl/cu121
pip install vllm
```

### Diagnostic Tools
```bash
# Run diagnostics
python docs/VLLM_TROUBLESHOOTING.md  # Contains diagnostic scripts

# Check CUDA
python -c "import torch; print(torch.cuda.is_available())"

# Memory check
nvidia-smi --query-gpu=memory.used,memory.total --format=csv
```

## 🔗 Integration with Your Project

### Add to Existing Node.js App
```javascript
// Add to your existing service
const { VLLMClient } = require('./examples/vllm_client.js');

class AIService {
    constructor() {
        this.vllmClient = new VLLMClient('http://localhost:8000');
    }
    
    async generateCarModification(carDescription, modifications) {
        const prompt = `Car: ${carDescription}\nModifications: ${modifications}\nRecommendations:`;
        return await this.vllmClient.complete({
            prompt,
            max_tokens: 500,
            temperature: 0.3
        });
    }
}
```

### Environment Variables
```bash
# Add to your .env file
VLLM_SERVER_URL=http://localhost:8000
VLLM_MODEL=mistralai/Mistral-7B-Instruct-v0.1
VLLM_MAX_TOKENS=512
VLLM_TEMPERATURE=0.7
```

## 📚 Additional Resources

- **[vLLM Documentation](https://docs.vllm.ai/)**
- **[Hugging Face Models](https://huggingface.co/models)**
- **[NVIDIA GTX 1080 Ti Specs](https://www.nvidia.com/en-us/geforce/10-series/)**

## 🤝 Support

For issues specific to this setup:
1. Check `docs/VLLM_TROUBLESHOOTING.md`
2. Run diagnostic scripts
3. Check GPU temperature and clocks
4. Verify CUDA installation

For vLLM issues:
- [vLLM GitHub Issues](https://github.com/vllm-project/vllm/issues)
- [vLLM Discord](https://discord.gg/jz7wjKhh6g)

## 📄 License

MIT License - see LICENSE file for details.
