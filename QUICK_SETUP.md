# Quick vLLM Setup for GTX 1080 (8GB VRAM)

This is a simplified setup guide for getting vLLM running quickly on your GTX 1080.

## ✅ Prerequisites Check

Your system should have:
- ✅ NVIDIA GTX 1080 (8GB VRAM) - **Detected**
- ✅ NVIDIA Driver 560.94 - **Installed**
- ✅ CUDA 12.6 - **Installed**
- ✅ Python 3.12.3 - **Installed**

## 🚀 Quick Installation Steps

### 1. Activate Virtual Environment
```bash
# Activate the environment we created
source vllm-env/bin/activate
```

### 2. Install vLLM (after PyTorch installation completes)
```bash
# Install vLLM
pip install vllm

# Install additional packages
pip install transformers accelerate huggingface_hub fastapi uvicorn requests psutil
```

### 3. Test Installation
```bash
# Test PyTorch CUDA
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}'); print(f'GPU: {torch.cuda.get_device_name(0)}')"

# Test vLLM
python -c "import vllm; print(f'vLLM version: {vllm.__version__}')"
```

## 🎯 Start Your First Model

### Option 1: Phi-3 Mini (Recommended for 8GB)
```bash
# Start server with Phi-3 Mini (fast, 4GB VRAM)
python src/vllm_server.py --model microsoft/Phi-3-mini-4k-instruct --model-size 3b --port 8000
```

### Option 2: TinyLlama (Ultra-fast)
```bash
# Start server with TinyLlama (very fast, 1GB VRAM)
python src/vllm_server.py --model TinyLlama/TinyLlama-1.1B-Chat-v1.0 --model-size 3b --port 8000
```

### Option 3: Mistral 7B AWQ (Quantized)
```bash
# Start server with quantized Mistral (good quality, 4GB VRAM)
python src/vllm_server.py --model TheBloke/Mistral-7B-Instruct-v0.1-AWQ --model-size 7b --port 8000
```

## 🧪 Test the Server

### 1. Health Check
```bash
curl http://localhost:8000/health
```

### 2. Simple Test
```bash
curl -X POST "http://localhost:8000/v1/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "What is artificial intelligence?",
    "max_tokens": 100,
    "temperature": 0.7
  }'
```

### 3. Python Client Test
```bash
python examples/vllm_client.py --example basic
```

## 📊 Monitor Performance

### GPU Usage
```bash
# Monitor GPU in real-time
nvidia-smi -l 1
```

### Server Stats
```bash
curl http://localhost:8000/stats
```

## 🔧 Troubleshooting

### Out of Memory Error
1. Try a smaller model (TinyLlama or Phi-3 Mini)
2. Reduce batch size in server config
3. Lower GPU memory utilization

### Slow Performance
1. Check GPU clocks: `nvidia-smi -q -d clock`
2. Ensure no other GPU processes are running
3. Try a quantized model (AWQ/GPTQ)

### Installation Issues
```bash
# Reinstall PyTorch if needed
pip uninstall torch vllm
pip install torch --index-url https://download.pytorch.org/whl/cu121
pip install vllm
```

## 🎉 Success!

Once you see:
```
INFO:     Uvicorn running on http://0.0.0.0:8000
✅ vLLM engine initialized successfully
```

Your local AI server is ready! You can now:
- Make API calls to `http://localhost:8000`
- Use the Python/Node.js clients in the `examples/` folder
- Integrate with your existing applications

## 📚 Next Steps

1. **Read the full documentation**: `docs/VLLM_SETUP_GUIDE.md`
2. **Performance tuning**: `docs/VLLM_PERFORMANCE_GUIDE.md`
3. **Troubleshooting**: `docs/VLLM_TROUBLESHOOTING.md`
4. **Try different models**: See `README_VLLM.md` for more options

## 💡 Pro Tips for GTX 1080

1. **Start small**: Begin with Phi-3 Mini or TinyLlama
2. **Monitor VRAM**: Keep usage under 7GB for stability
3. **Use quantized models**: AWQ/GPTQ models are much more efficient
4. **Batch wisely**: Smaller batch sizes work better on 8GB cards
5. **Temperature control**: Keep GPU cool for consistent performance

Happy AI serving! 🚀
